"use client"

import { LayoutDashboard, ListMusic, Menu, Music, PlaySquare, X } from "lucide-react"
import Link from "next/link"
import { usePathname } from "next/navigation"

import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"

import { useSidebar } from "./sidebar-context"

interface AdminSidebarProps {
  className?: string
}

export function AdminSidebar({ className }: AdminSidebarProps) {
  const pathname = usePathname()
  const { isCollapsed, toggleSidebar } = useSidebar()

  // Define admin routes
  const adminRoutes = [
    {
      icon: LayoutDashboard,
      href: "/admin",
      label: "Dashboard",
      isActive: pathname === "/admin",
    },
    {
      icon: Music,
      href: "/admin/musics",
      label: "Musics",
      isActive: pathname.startsWith("/admin/musics"),
    },
    {
      icon: ListMusic,
      href: "/admin/music-playlists",
      label: "Music Playlists",
      isActive: pathname.startsWith("/admin/music-playlists"),
    },
    {
      icon: ListMusic,
      href: "/admin/nature-playlists",
      label: "Nature Playlists",
      isActive: pathname.startsWith("/admin/nature-playlists"),
    },
    {
      icon: PlaySquare,
      href: "/admin/videos",
      label: "Videos",
      isActive: pathname.startsWith("/admin/videos"),
    },
  ]

  return (
    <div 
      className={cn(
        "flex h-full flex-col border-r bg-background transition-all duration-200",
        isCollapsed ? "w-[60px]" : "w-[250px]",
        className
      )}
    >
      <div className="flex h-14 items-center border-b px-4">
        {!isCollapsed && (
          <h1 className="text-xl font-semibold">Home</h1>
        )}
        <Button
          variant="ghost"
          size="icon"
          onClick={toggleSidebar}
          className={cn("h-8 w-8 bg-neutral-300/20 hover:bg-neutral-300/30 dark:bg-neutral-400/20 dark:hover:bg-neutral-400/30 text-white backdrop-blur-[1px] border border-neutral-400/20 hover:border-neutral-400/30", isCollapsed ? "mx-auto" : "ml-auto")}
          aria-label={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
        >
          {isCollapsed ? <Menu className="h-5 w-5" /> : <X className="h-5 w-5" />}
        </Button>
      </div>
      <div className="flex-1 py-4">
        <nav className="grid gap-1 px-2">
          {adminRoutes.map((route) => (
            <Link
              key={route.href}
              href={route.href}
              className={cn(
                "flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium",
                route.isActive
                  ? "bg-muted"
                  : "text-muted-foreground hover:bg-muted hover:text-foreground"
              )}
            >
              <route.icon className={cn("h-5 w-5", route.isActive ? "text-foreground" : "text-muted-foreground")} />
              {!isCollapsed && <span>{route.label}</span>}
            </Link>
          ))}
        </nav>
      </div>
      {!isCollapsed && (
        <div className="border-t p-4">
          <p className="text-xs text-muted-foreground">Admin Dashboard v1.0</p>
        </div>
      )}
    </div>
  )
} 