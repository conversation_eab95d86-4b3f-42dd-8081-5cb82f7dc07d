'use client';

import { useEffect } from 'react';
import { usePomodoroStore } from '@/lib/pomodoro-store';

interface TimerAutoBehaviorsProps {
  autoStartPomodoros: boolean;
  currentPhase: string;
  isRunning: boolean;
  startTimer: () => void;
  children?: React.ReactNode;
}

export function TimerAutoBehaviors({
  autoStartPomodoros,
  currentPhase,
  isRunning,
  startTimer,
  children
}: TimerAutoBehaviorsProps) {
  
  // Auto-start pomodoro timer when page loads if setting is enabled
  useEffect(() => {
    if (autoStartPomodoros && currentPhase === 'pomodoro' && !isRunning) {
      // Get the manuallyPaused state from the store
      const manuallyPaused = usePomodoroStore.getState().manuallyPaused;

      // Only auto-start if the timer was not manually paused
      if (!manuallyPaused) {
        // Small delay to ensure everything is loaded
        const autoStartTimer = setTimeout(() => {
          startTimer();
        }, 500);

        return () => clearTimeout(autoStartTimer);
      }
    }
  }, [autoStartPomodoros, currentPhase, isRunning, startTimer]);

  return <>{children}</>;
}
