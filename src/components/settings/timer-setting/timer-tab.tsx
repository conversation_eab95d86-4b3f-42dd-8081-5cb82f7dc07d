'use client';

import { Clock, RefreshCw, Calendar, Timer, TrendingUp } from 'lucide-react';
import { Slider } from '@/components/ui/slider';
import { Input } from '@/components/ui/input';
import { Badge } from "@/components/ui/badge";
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import { TabComponentProps } from './types';
import { TimerCycleVisualization } from '../timer';
import { TimerFormValues } from '../timer';

export function TimerTab({
  handleNumberChange,
  pomodoroValue,
  shortBreakValue,
  longBreakValue,
  sessionsValue,
  timerModeValue,
  handleTimerModeChange,
  calculateTotalDuration,
  calculateEndTime
}: TabComponentProps) {
  // Common section class for consistent spacing and styling
  const sectionClass = "space-y-1.5 mb-3";
  const headerClass = "flex justify-between items-center mb-1.5";
  const titleClass = "text-xs font-medium flex items-center gap-1.5 text-foreground/80";
  const contentClass = "flex items-center gap-2.5";

  // Generic function to render a timer setting control
  const renderTimerControl = (
    title: string,
    value: number,
    unit: string,
    field: keyof TimerFormValues,
    min: number,
    max: number,
    styleKey: string
  ) => {
    // Get icon based on the type of timer setting
    const getIcon = () => {
      switch(styleKey) {
        case 'pomodoro':
          return <Clock className="h-3 w-3 text-primary/70" />;
        case 'shortBreak':
          return <RefreshCw className="h-3 w-3 text-emerald-500/70" />;
        case 'longBreak':
          return <RefreshCw className="h-3 w-3 text-purple-500/70" />;
        case 'sessions':
          return <Calendar className="h-3 w-3 text-orange-500/70" />;
        default:
          return <Clock className="h-3 w-3 text-primary/70" />;
      }
    };

    return (
      <div className={sectionClass}>
        <div className={headerClass}>
          <h3 className={titleClass}>
            {getIcon()}
            <span>{title}</span>
          </h3>
          <div className="flex items-center gap-1 text-xs">
            <Badge variant="outline" className="h-4 px-1.5 text-[10px] font-normal bg-muted/30 border-border/50">
              {value} {unit}
            </Badge>
          </div>
        </div>

        <div className={contentClass}>
          <Slider
            value={[value]}
            min={min}
            max={max}
            step={1}
            className="flex-1"
            onValueChange={(values) => handleNumberChange(field, values[0])}
          />
          <Input
            type="number"
            value={value}
            onChange={(e) => handleNumberChange(field, parseInt(e.target.value) || min)}
            className="w-16 h-6 text-center text-xs border-border/50 bg-background/50"
            min={min}
            max={max}
          />
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-3">
      {/* Timer Mode Toggle */}
      <div className={sectionClass}>
        <div className={headerClass}>
          <h3 className={titleClass}>
            <Timer className="h-3 w-3 text-primary/70" />
            <span>Focus Timer Mode</span>
          </h3>
          <div className="flex items-center gap-1">
            <Badge
              variant="outline"
              className={`h-4 px-1.5 text-[9px] font-medium border transition-colors ${
                timerModeValue === 'countDown'
                  ? 'bg-blue-50/80 text-blue-600 border-blue-200/60 dark:bg-blue-950/15 dark:text-blue-400 dark:border-blue-800/40'
                  : 'bg-emerald-50/80 text-emerald-600 border-emerald-200/60 dark:bg-emerald-950/15 dark:text-emerald-400 dark:border-emerald-800/40'
              }`}
            >
              {timerModeValue === 'countDown' ? 'Countdown' : 'Count Up'}
            </Badge>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <ToggleGroup
            type="single"
            value={timerModeValue}
            onValueChange={(value) => value && handleTimerModeChange(value as 'countDown' | 'countUp')}
            className="grid w-full grid-cols-2 gap-1.5 cursor-pointer"
          >
            <ToggleGroupItem
              value="countDown"
              className={`relative text-xs py-2.5 px-3 transition-all duration-200 font-medium border-0 overflow-hidden cursor-pointer ${
                timerModeValue === 'countDown'
                  ? '!bg-blue-50 hover:!bg-blue-100 !text-blue-700 dark:!bg-blue-950/20 dark:!text-blue-400 dark:hover:!bg-blue-950/30'
                  : '!bg-blue-50/50 hover:!bg-blue-50 !text-blue-600/70 hover:!text-blue-700 dark:!bg-blue-950/10 dark:!text-blue-400/70 dark:hover:!bg-blue-950/20 dark:hover:!text-blue-400'
              }`}
            >
              <TrendingUp className="h-3.5 w-3.5 mr-1.5 rotate-180" />
              Count Down
              {timerModeValue === 'countDown' && (
                <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-blue-500 dark:bg-blue-400" />
              )}
            </ToggleGroupItem>
            <ToggleGroupItem
              value="countUp"
              className={`relative text-xs py-2.5 px-3 transition-all duration-200 font-medium border-0 overflow-hidden cursor-pointer ${
                timerModeValue === 'countUp'
                  ? '!bg-emerald-50 hover:!bg-emerald-100 !text-emerald-700 dark:!bg-emerald-950/20 dark:!text-emerald-400 dark:hover:!bg-emerald-950/30'
                  : '!bg-emerald-50/50 hover:!bg-emerald-50 !text-emerald-600/70 hover:!text-emerald-700 dark:!bg-emerald-950/10 dark:!text-emerald-400/70 dark:hover:!bg-emerald-950/20 dark:hover:!text-emerald-400'
              }`}
            >
              <TrendingUp className="h-3.5 w-3.5 mr-1.5" />
              Count Up
              {timerModeValue === 'countUp' && (
                <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-emerald-500 dark:bg-emerald-400" />
              )}
            </ToggleGroupItem>
          </ToggleGroup>
        </div>

        {/* Enhanced descriptive text with better formatting and detailed explanations */}
        <div className={`mt-3 p-3 rounded-lg border transition-all duration-200 ${
          timerModeValue === 'countDown'
            ? 'bg-blue-50/30 border-blue-200/40 dark:bg-blue-950/8 dark:border-blue-800/30'
            : 'bg-emerald-50/30 border-emerald-200/40 dark:bg-emerald-950/8 dark:border-emerald-800/30'
        }`}>
          {timerModeValue === 'countDown' ? (
            <div className="space-y-1.5">
              <div className="flex items-center gap-2">
                <div className="w-2.5 h-2.5 rounded-full bg-blue-500 shadow-sm"></div>
                <span className="text-xs font-semibold text-blue-800 dark:text-blue-300">Countdown Timer</span>
              </div>

              <div className="ml-4.5">
                <ul className="text-[9px] text-blue-600/90 dark:text-blue-400/90 space-y-0.5 leading-relaxed">
                  <li>• Timer counts down: <span className="font-mono bg-blue-100 dark:bg-blue-900/30 px-1 rounded">25:00 → 00:00</span></li>
                  <li>• Starts break when finished</li>
                </ul>
              </div>
            </div>
          ) : (
            <div className="space-y-1.5">
              <div className="flex items-center gap-2">
                <div className="w-2.5 h-2.5 rounded-full bg-emerald-500 shadow-sm"></div>
                <span className="text-xs font-semibold text-emerald-800 dark:text-emerald-300">Count Up Timer</span>
              </div>

              <div className="ml-4.5">
                <ul className="text-[9px] text-emerald-600/90 dark:text-emerald-400/90 space-y-0.5 leading-relaxed">
                  <li>• Timer counts up: <span className="font-mono bg-emerald-100 dark:bg-emerald-900/30 px-1 rounded">00:00 → 00:01 → ...</span></li>
                  <li>• You manually stop when ready for break</li>
                </ul>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Timer Settings Sections */}
      <div className="space-y-2.5">
        {/* Focus Duration - Only show in count down mode */}
        {timerModeValue === 'countDown' && renderTimerControl(
          "Focus Duration",
          pomodoroValue,
          "minutes",
          'pomodoroMinutes',
          1,
          180,
          'pomodoro'
        )}

        {/* Short Break Duration */}
        {renderTimerControl(
          "Short Break Duration",
          shortBreakValue,
          "minutes",
          'shortBreakMinutes',
          1,
          30,
          'shortBreak'
        )}

        {/* Long Break Duration */}
        {renderTimerControl(
          "Long Break Duration",
          longBreakValue,
          "minutes",
          'longBreakMinutes',
          1,
          60,
          'longBreak'
        )}

        {/* Sessions Until Long Break */}
        {renderTimerControl(
          "Sessions Until Long Break",
          sessionsValue,
          sessionsValue === 1 ? 'session' : 'sessions',
          'sessionsBeforeLongBreak',
          1,
          10,
          'sessions'
        )}
      </div>

      {/* Timer cycle visualization */}
      <div className="mt-4 pt-3 border-t border-border/40">
        <h3 className="text-xs font-medium mb-2.5 flex items-center gap-1.5 text-foreground/80">
          <RefreshCw className="h-3 w-3 text-primary/70" />
          <span>Cycle Preview</span>
        </h3>

        <div className="max-h-26 overflow-y-auto px-1" style={{ WebkitOverflowScrolling: 'touch' }}>
          <TimerCycleVisualization
            pomodoroMinutes={pomodoroValue}
            shortBreakMinutes={shortBreakValue}
            longBreakMinutes={longBreakValue}
            sessionsCount={sessionsValue}
            timerMode={timerModeValue}
          />
        </div>

        <div className="mt-3 pt-2.5 border-t border-border/40">
          <div className="flex flex-col space-y-1 sm:flex-row sm:justify-between sm:space-y-0 text-[9px] text-muted-foreground">
            <div className="bg-muted/30 px-1.5 py-0.5 rounded-sm">
              <span className="font-medium">
                {sessionsValue} focus {sessionsValue === 1 ? 'session' : 'sessions'}
                {timerModeValue === 'countUp' ? ' (manual stop)' : ''}, {sessionsValue - 1} short {(sessionsValue - 1) === 1 ? 'break' : 'breaks'}, 1 long break
              </span>
            </div>
            <div className="flex items-center gap-1.5">
              {timerModeValue === 'countDown' ? (
                <>
                  <div className="flex items-center gap-0.5 bg-muted/30 px-1.5 py-0.5 rounded-sm">
                    <Clock className="h-2.5 w-2.5 text-primary/70" />
                    <span className="font-medium">Total: {calculateTotalDuration(pomodoroValue, shortBreakValue, longBreakValue, sessionsValue)}</span>
                  </div>
                  <div className="flex items-center gap-0.5 bg-muted/30 px-1.5 py-0.5 rounded-sm">
                    <Calendar className="h-2.5 w-2.5 text-orange-500/70" />
                    <span className="font-medium">Ends: {calculateEndTime(pomodoroValue, shortBreakValue, longBreakValue, sessionsValue)}</span>
                  </div>
                </>
              ) : (
                <div className="flex items-center gap-0.5 bg-muted/30 px-1.5 py-0.5 rounded-sm">
                  <Clock className="h-2.5 w-2.5 text-primary/70" />
                  <span className="font-medium">Duration: Variable (you control focus time)</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
