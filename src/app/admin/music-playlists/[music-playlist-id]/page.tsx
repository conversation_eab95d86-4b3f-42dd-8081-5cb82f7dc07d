"use client";

import { useGetAdminMusicPlaylist, useDeleteAdminMusicPlaylist, type GetAdminMusicPlaylist_ResponseTypeSuccess } from "@schemas/MusicPlaylist/music-playlist-admin-query";
import { useGetMusics } from "@schemas/Music/music-query";
import { useGetVideos } from "@schemas/Video/video-query";
import { notFound, useParams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "sonner";
import { useAddMusicToAdminMusicPlaylist, useAddVideosToAdminMusicPlaylist } from "@schemas/MusicPlaylist/music-playlist-admin-query";
import { MusicGenre } from "@prisma/client";
import { Button } from "@/components/ui/button";
import {
  ArrowLeft,
  Music2,
  Video,
  Edit,
  Trash2,
  Clock,
  Calendar,
  User
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import Image from "next/image";
import { cn } from "@/lib/utils";
import { formatGenreName, truncateGenres } from "@/lib/genre-utils";

// Import the components
import { MediaTabs, AddItemsDialog } from "./_components";
import { MusicPlaylistFormSheet } from "../_components/music-playlist-form-sheet";

// Extended type to include genres field
type MusicPlaylistWithGenres = GetAdminMusicPlaylist_ResponseTypeSuccess & {
  genres: MusicGenre[];
};

export default function MusicPlaylistPage() {
  const router = useRouter();
  const params = useParams<{ "music-playlist-id": string }>();
  const musicPlaylistId = params["music-playlist-id"];
  const [activeTab, setActiveTab] = useState("music");

  // Dialog state
  const [addDialogOpen, setAddDialogOpen] = useState(false);
  const [addDialogType, setAddDialogType] = useState<"music" | "video">("music");
  const [searchQuery, setSearchQuery] = useState("");
  const [isEditSheetOpen, setIsEditSheetOpen] = useState(false);

  // Selection state
  const [selectedMusicIds, setSelectedMusicIds] = useState<string[]>([]);
  const [selectedVideoIds, setSelectedVideoIds] = useState<string[]>([]);

  // Data fetching
  const { data: musicPlaylistData, isLoading, refetch } = useGetAdminMusicPlaylist(musicPlaylistId);
  console.log("musicPlaylistData", musicPlaylistData);
  const musicPlaylist = musicPlaylistData as MusicPlaylistWithGenres | undefined;
  const { data: allMusic } = useGetMusics();
  const { data: allVideos } = useGetVideos();

  // Delete mutation
  const deleteMusicPlaylist = useDeleteAdminMusicPlaylist();

  // Add items mutations
  const addMusicToMusicPlaylist = useAddMusicToAdminMusicPlaylist();
  const addVideosToMusicPlaylist = useAddVideosToAdminMusicPlaylist();

  // Extract existing IDs for filtering
  const existingMusicIds = musicPlaylist?.musics?.map(music => music.id) || [];

  // Filter available items that are not already in the playlist
  const availableMusic = allMusic?.filter(music =>
    !existingMusicIds.includes(music.id)
  ) || [];

  // Filter available videos that are not already in a music playlist (can have nature playlist)
  const availableVideos = allVideos?.filter(video =>
    !video.musicPlaylistId // Video is not in any music playlist
  ) || [];

  // Calculate total items
  const totalItems = musicPlaylist ?
    musicPlaylist.videos.length + musicPlaylist.musics.length : 0;

  // Set page metadata
  useEffect(() => {
    if (musicPlaylist) {
      document.title = `${musicPlaylist.name} | Pomodoro 365 Admin`;

      const metaDescription = document.querySelector('meta[name="description"]');
      if (metaDescription) {
        metaDescription.setAttribute('content', musicPlaylist.description || `Music Playlist with ${totalItems} items`);
      } else {
        const meta = document.createElement('meta');
        meta.name = "description";
        meta.content = musicPlaylist.description || `Music Playlist with ${totalItems} items`;
        document.head.appendChild(meta);
      }

      if (musicPlaylist.imageUrl) {
        const ogImage = document.querySelector('meta[property="og:image"]');
        if (ogImage) {
          ogImage.setAttribute('content', musicPlaylist.imageUrl);
        } else {
          const meta = document.createElement('meta');
          meta.setAttribute('property', 'og:image');
          meta.content = musicPlaylist.imageUrl;
          document.head.appendChild(meta);
        }
      }
    }
  }, [musicPlaylist, totalItems]);

  const handleDelete = async () => {
    try {
      await deleteMusicPlaylist.mutateAsync({ id: musicPlaylistId });
      router.push("/admin/music-playlists");
    } catch {
      toast.error("Failed to delete music playlist");
    }
  };

  const handleOpenAddDialog = (type: "music" | "video") => {
    setAddDialogType(type);
    setSearchQuery("");
    setSelectedMusicIds([]);
    setSelectedVideoIds([]);
    setAddDialogOpen(true);
  };

  const handleAddItems = async () => {
    if (!musicPlaylist) return;

    try {
      if (addDialogType === "music" && selectedMusicIds.length > 0) {
        await addMusicToMusicPlaylist.mutateAsync({
          musicPlaylistId,
          musicIds: selectedMusicIds
        });
        toast.success(`${selectedMusicIds.length} music tracks added to playlist`);
        refetch();
      } else if (addDialogType === "video" && selectedVideoIds.length > 0) {
        await addVideosToMusicPlaylist.mutateAsync({
          musicPlaylistId,
          videoIds: selectedVideoIds
        });
        toast.success(`${selectedVideoIds.length} videos added to playlist`);
        refetch();
      }

      setAddDialogOpen(false);
    } catch (error) {
      toast.error(`Failed to update playlist: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  };

  const handleItemSelect = (id: string, checked: boolean) => {
    if (addDialogType === "music") {
      if (checked) {
        setSelectedMusicIds(prev => [...prev, id]);
      } else {
        setSelectedMusicIds(prev => prev.filter(itemId => itemId !== id));
      }
    } else {
      if (checked) {
        setSelectedVideoIds(prev => [...prev, id]);
      } else {
        setSelectedVideoIds(prev => prev.filter(itemId => itemId !== id));
      }
    }
  };

  const handleSelectAll = (filteredItems: any[]) => {
    if (addDialogType === "music") {
      const currentSelectedIds = selectedMusicIds;
      const filteredItemIds = filteredItems.map(item => item.id);

      // Check if all filtered items are already selected
      const allFilteredSelected = filteredItemIds.every(id => currentSelectedIds.includes(id));

      if (allFilteredSelected) {
        // Deselect all filtered items
        setSelectedMusicIds(prev => prev.filter(id => !filteredItemIds.includes(id)));
      } else {
        // Select all filtered items (add only new ones)
        const newIds = filteredItemIds.filter(id => !currentSelectedIds.includes(id));
        setSelectedMusicIds(prev => [...prev, ...newIds]);
      }
    } else if (addDialogType === "video") {
      const currentSelectedIds = selectedVideoIds;
      const filteredItemIds = filteredItems.map(item => item.id);

      // Check if all filtered items are already selected
      const allFilteredSelected = filteredItemIds.every(id => currentSelectedIds.includes(id));

      if (allFilteredSelected) {
        // Deselect all filtered items
        setSelectedVideoIds(prev => prev.filter(id => !filteredItemIds.includes(id)));
      } else {
        // Select all filtered items (add only new ones)
        const newIds = filteredItemIds.filter(id => !currentSelectedIds.includes(id));
        setSelectedVideoIds(prev => [...prev, ...newIds]);
      }
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="container mx-auto py-8 px-4 md:px-6">
        <div className="flex items-center gap-2 mb-8">
          <Button variant="ghost" size="sm" className="gap-1">
            <ArrowLeft className="h-4 w-4" />
            <Skeleton className="h-4 w-20" />
          </Button>
        </div>

        <div className="space-y-8">
          {/* Header skeleton */}
          <div className="relative rounded-xl overflow-hidden">
            <Skeleton className="w-full h-48 md:h-64" />
            <div className="absolute bottom-0 left-0 right-0 p-6 bg-gradient-to-t from-black/80 to-transparent">
              <div className="space-y-4">
                <Skeleton className="h-8 w-1/2" />
                <Skeleton className="h-4 w-3/4" />
                <div className="flex gap-2">
                  <Skeleton className="h-6 w-16 rounded-full" />
                  <Skeleton className="h-6 w-16 rounded-full" />
                </div>
              </div>
            </div>
          </div>

          {/* Tabs skeleton */}
          <div className="space-y-6">
            <Skeleton className="h-10 w-72" />
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {Array.from({ length: 8 }).map((_, i) => (
                <div key={i} className="space-y-3">
                  <Skeleton className="aspect-video rounded-lg" />
                  <div className="space-y-2">
                    <Skeleton className="h-5 w-3/4" />
                    <Skeleton className="h-4 w-1/2" />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!musicPlaylist) {
    notFound();
  }

  return (
    <div className="container mx-auto py-8 px-4 md:px-6">
      {/* Back button */}
      <div className="flex items-center gap-2 mb-8">
        <Button
          variant="ghost"
          size="sm"
          className="gap-1 hover:bg-muted"
          onClick={() => router.push("/admin/music-playlists")}
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Music Playlists
        </Button>
      </div>

      <div className="space-y-8">
        {/* Enhanced Header with Cover Image */}
        <div className="relative rounded-xl overflow-hidden bg-gradient-to-r from-rose-50 to-rose-100 dark:from-rose-950/20 dark:to-rose-900/20">
          <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/60 z-10" />
          
          {musicPlaylist.imageUrl && (
            <div className="absolute inset-0">
              <Image
                src={musicPlaylist.imageUrl}
                alt={musicPlaylist.name}
                fill
                className="object-cover"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 75vw"
                priority
              />
            </div>
          )}
          
          <div className="relative z-20 p-6 md:p-8">
            <div className="flex flex-col md:flex-row gap-6 items-start">
              <div className="flex-1 min-w-0">
                <h1 className="text-2xl md:text-3xl font-bold text-white mb-2">{musicPlaylist.name}</h1>
                {musicPlaylist.description && (
                  <p className="text-white/90 mb-4 max-w-2xl">{musicPlaylist.description}</p>
                )}
                
                <div className="flex flex-wrap gap-3 mb-4">
                  {musicPlaylist.isPublic ? (
                    <Badge variant="secondary" className="bg-emerald-500/20 text-emerald-200">Public</Badge>
                  ) : (
                    <Badge variant="secondary" className="bg-amber-500/20 text-amber-200">Private</Badge>
                  )}

                  <Badge variant="secondary" className="bg-white/20 text-white">
                    <Music2 className="h-3 w-3 mr-1" />
                    {musicPlaylist.musics.length} Tracks
                  </Badge>

                  <Badge variant="secondary" className="bg-white/20 text-white">
                    <Video className="h-3 w-3 mr-1" />
                    {musicPlaylist.videos.length} Videos
                  </Badge>
                </div>

                {/* Genres */}
                {musicPlaylist.genres && musicPlaylist.genres.length > 0 && (
                  <div className="flex flex-wrap gap-2 mb-4">
                    {truncateGenres(musicPlaylist.genres, 4).visible.map((genre) => (
                      <Badge
                        key={genre}
                        variant="outline"
                        className={cn(
                          "text-xs border-white/30 text-white/90 bg-white/10 hover:bg-white/20 transition-colors",
                          "backdrop-blur-sm"
                        )}
                      >
                        {formatGenreName(genre)}
                      </Badge>
                    ))}
                    {truncateGenres(musicPlaylist.genres, 4).remaining > 0 && (
                      <Badge
                        variant="outline"
                        className="text-xs border-white/30 text-white/70 bg-white/10"
                      >
                        +{truncateGenres(musicPlaylist.genres, 4).remaining}
                      </Badge>
                    )}
                  </div>
                )}
                
                <div className="flex flex-wrap gap-2 text-xs text-white/70">
                  <div className="flex items-center">
                    <Calendar className="h-3 w-3 mr-1" />
                    Created {format(new Date(musicPlaylist.createdAt), "MMM d, yyyy")}
                  </div>
                  
                  <div className="flex items-center">
                    <Clock className="h-3 w-3 mr-1" />
                    Updated {format(new Date(musicPlaylist.updatedAt), "MMM d, yyyy")}
                  </div>
                  
                  <div className="flex items-center">
                    <User className="h-3 w-3 mr-1" />
                    {musicPlaylist.creatorType}
                  </div>
                </div>
              </div>
              
              <div className="flex flex-row md:flex-col gap-2 md:self-start">

                
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="secondary"
                        size="icon"
                        className="bg-white/20 text-white hover:bg-white/30"
                        onClick={() => setIsEditSheetOpen(true)}
                      >
                        <Edit className="h-4 w-4" />
                        <span className="sr-only">Edit</span>
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Edit this playlist</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="secondary"
                        size="icon"
                        className="bg-white/20 text-white hover:bg-white/30"
                        onClick={handleDelete}
                      >
                        <Trash2 className="h-4 w-4" />
                        <span className="sr-only">Delete</span>
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Delete this playlist</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
          </div>
        </div>

        {/* Media content */}
        <MediaTabs
          musicPlaylist={musicPlaylist}
          activeTab={activeTab}
          setActiveTab={setActiveTab}
          onAddMusic={() => handleOpenAddDialog("music")}
          onAddVideo={() => handleOpenAddDialog("video")}
          onRefetch={refetch}
        />
      </div>
      
      {/* Add Items Dialog */}
      <AddItemsDialog
        open={addDialogOpen}
        onOpenChange={setAddDialogOpen}
        type={addDialogType}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        availableItems={addDialogType === "music" ? availableMusic : availableVideos}
        selectedIds={addDialogType === "music" ? selectedMusicIds : selectedVideoIds}
        onSelect={handleItemSelect}
        onSelectAll={handleSelectAll}
        onAdd={handleAddItems}
        currentPlaylist={musicPlaylist}
      />

      {/* Edit Music Playlist Sheet */}
      <MusicPlaylistFormSheet
        playlistId={musicPlaylistId}
        open={isEditSheetOpen}
        onOpenChange={setIsEditSheetOpen}
        onSuccess={() => {
          setIsEditSheetOpen(false);
          refetch();
        }}
      />
    </div>
  );
}