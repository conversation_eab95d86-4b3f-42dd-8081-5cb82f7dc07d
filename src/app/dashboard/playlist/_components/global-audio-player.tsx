"use client"

import { useEffect, useRef, useCallback, useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Slider } from "@/components/ui/slider"
import { Badge } from "@/components/ui/badge"
import { useSidebar } from "@/components/ui/sidebar"
import {
  Play,
  Pause,
  Volume2,
  VolumeX,
  X,
  Music2,
  Waves
} from "lucide-react"
import { useAudioStore } from "@/lib/audio-store"
import { motion, AnimatePresence } from "framer-motion"
import { cn } from "@/lib/utils"
import { NatureSoundsPlayer } from "@/components/audio/nature-sounds-player"

export function GlobalAudioPlayer() {
  const {
    globalPlayer,
    stopGlobalPlayer,
    setGlobalPlayerPlaying,
    skipToNextTrack,
    toggleNatureSound,
    setNatureSoundVolume,
    toggleNatureSoundMute,
    setMusicVolume,
    toggleMusicMute,
  } = useAudioStore()

  const { state: sidebarState, isMobile } = useSidebar()
  const audioRef = useRef<HTMLAudioElement | null>(null)
  const playerRef = useRef<HTMLDivElement | null>(null)

  // Local state for audio playback (like the working admin player)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)

  // Update CSS variable with actual player height
  const updatePlayerHeight = useCallback(() => {
    if (playerRef.current) {
      const height = playerRef.current.offsetHeight
      // Add a small buffer (16px) to ensure content is never hidden
      const heightWithBuffer = height + 16
      document.documentElement.style.setProperty('--global-audio-player-height', `${heightWithBuffer}px`)
    } else {
      // Player is not visible, set height to 0
      document.documentElement.style.setProperty('--global-audio-player-height', '0px')
    }
  }, [])

  // Update height when player visibility or content changes
  useEffect(() => {
    updatePlayerHeight()

    // Use ResizeObserver to detect height changes
    if (playerRef.current) {
      const resizeObserver = new ResizeObserver(() => {
        updatePlayerHeight()
      })

      resizeObserver.observe(playerRef.current)

      return () => {
        resizeObserver.disconnect()
      }
    }
  }, [updatePlayerHeight, globalPlayer.currentTrack, globalPlayer.areNatureSoundsPlaying, globalPlayer.natureSounds.length])

  // Clean up CSS variable on unmount
  useEffect(() => {
    return () => {
      document.documentElement.style.setProperty('--global-audio-player-height', '0px')
    }
  }, [])

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  // Handle time update event (using local state like admin player)
  const handleTimeUpdate = useCallback(() => {
    if (audioRef.current) {
      setCurrentTime(audioRef.current.currentTime)
    }
  }, [])

  // Handle loaded metadata event (using local state like admin player)
  const handleLoadedMetadata = useCallback(() => {
    if (audioRef.current && !isNaN(audioRef.current.duration)) {
      setDuration(audioRef.current.duration)
      setCurrentTime(0) // Ensure current time is reset
    }
  }, [])

  // Handle play/pause toggle (using local state like admin player)
  const handlePlayPause = useCallback(() => {
    if (!audioRef.current) return

    if (isPlaying) {
      audioRef.current.pause()
    } else {
      audioRef.current.play().catch(error => {
        console.error("Play failed:", error)
      })
    }
  }, [isPlaying])

  // Sync local isPlaying state with global store when external commands change it
  useEffect(() => {
    if (!audioRef.current) return

    if (globalPlayer.isPlaying && !isPlaying) {
      // External command to play
      audioRef.current.play().catch(error => {
        console.error("Play failed:", error)
        setGlobalPlayerPlaying(false)
      })
    } else if (!globalPlayer.isPlaying && isPlaying) {
      // External command to pause
      audioRef.current.pause()
    }
  }, [globalPlayer.isPlaying, isPlaying, setGlobalPlayerPlaying])

  // Handle music volume toggle
  const handleMusicVolumeToggle = useCallback(() => {
    toggleMusicMute()
  }, [toggleMusicMute])

  // Handle music volume change with debouncing to prevent excessive updates
  const handleMusicVolumeChange = useCallback((value: number[]) => {
    const newVolume = value[0]
    setMusicVolume(newVolume)

    // If volume is increased from 0 and music is muted, unmute it
    if (newVolume > 0 && globalPlayer.musicIsMuted) {
      toggleMusicMute()
    }
  }, [setMusicVolume, globalPlayer.musicIsMuted, toggleMusicMute])

  // Handle seeking in the timeline
  const handleSeek = useCallback((value: number[]) => {
    const newTime = value[0]
    if (audioRef.current) {
      audioRef.current.currentTime = newTime
      setCurrentTime(newTime)
    }
  }, [])

  // Handle close player
  const handleClose = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.pause()
    }
    stopGlobalPlayer()
  }, [stopGlobalPlayer])

  // Create/update audio element and set up event listeners (like admin player)
  useEffect(() => {
    if (!globalPlayer.currentTrack?.src) {
      setIsPlaying(false)
      setCurrentTime(0)
      setDuration(0)
      return
    }

    // Reset state when track changes
    setCurrentTime(0)
    setDuration(0)

    // Create audio element if it doesn't exist
    if (!audioRef.current) {
      audioRef.current = new Audio()
    }

    // Define event handlers with stable references
    const handlePlay = () => {
      setIsPlaying(true)
      setGlobalPlayerPlaying(true)
    }

    const handlePause = () => {
      setIsPlaying(false)
      setGlobalPlayerPlaying(false)
    }

    const handleEnded = () => {
      setIsPlaying(false)

      // If in playlist mode, try to advance to next track
      if (globalPlayer.isPlaylistMode) {
        skipToNextTrack()
      } else {
        stopGlobalPlayer()
      }
    }

    // Remove any existing event listeners first
    audioRef.current.removeEventListener("play", handlePlay)
    audioRef.current.removeEventListener("pause", handlePause)
    audioRef.current.removeEventListener("ended", handleEnded)
    audioRef.current.removeEventListener("timeupdate", handleTimeUpdate)
    audioRef.current.removeEventListener("loadedmetadata", handleLoadedMetadata)

    // Add fresh event listeners
    audioRef.current.addEventListener("play", handlePlay)
    audioRef.current.addEventListener("pause", handlePause)
    audioRef.current.addEventListener("ended", handleEnded)
    audioRef.current.addEventListener("timeupdate", handleTimeUpdate)
    audioRef.current.addEventListener("loadedmetadata", handleLoadedMetadata)

    // Update audio source if it changed
    if (audioRef.current.src !== globalPlayer.currentTrack.src) {
      audioRef.current.src = globalPlayer.currentTrack.src
      audioRef.current.load()
      // Reset time values immediately when loading new track
      setCurrentTime(0)
      setDuration(0)
    }

    // Play the audio
    const playPromise = audioRef.current.play()

    // Handle autoplay restrictions
    if (playPromise !== undefined) {
      playPromise.catch(error => {
        console.error("Autoplay prevented:", error)
        setIsPlaying(false)
      })
    }

    // Set initial volume & muted state
    audioRef.current.volume = globalPlayer.musicIsMuted ? 0 : globalPlayer.musicVolume / 100

    // Cleanup function
    return () => {
      if (audioRef.current) {
        audioRef.current.removeEventListener("play", handlePlay)
        audioRef.current.removeEventListener("pause", handlePause)
        audioRef.current.removeEventListener("ended", handleEnded)
        audioRef.current.removeEventListener("timeupdate", handleTimeUpdate)
        audioRef.current.removeEventListener("loadedmetadata", handleLoadedMetadata)
        audioRef.current.pause()
      }
    }
  }, [globalPlayer.currentTrack?.src, globalPlayer.isPlaylistMode, globalPlayer.musicIsMuted, globalPlayer.musicVolume, stopGlobalPlayer, skipToNextTrack, handleTimeUpdate, handleLoadedMetadata, setGlobalPlayerPlaying])

  // Reset progress when track changes (track ID change)
  useEffect(() => {
    if (globalPlayer.currentTrack?.id) {
      setCurrentTime(0)
      setDuration(0)
    }
  }, [globalPlayer.currentTrack?.id])

  // Update volume when it changes (separate from main audio setup to prevent flickering)
  useEffect(() => {
    if (audioRef.current) {
      const newVolume = globalPlayer.musicIsMuted ? 0 : globalPlayer.musicVolume / 100
      // Only update if the volume actually changed to prevent unnecessary DOM updates
      if (Math.abs(audioRef.current.volume - newVolume) > 0.001) {
        audioRef.current.volume = newVolume
      }
    }
  }, [globalPlayer.musicVolume, globalPlayer.musicIsMuted])

  // Show player if there's a current track OR nature sounds are playing
  if (!globalPlayer.currentTrack && !globalPlayer.areNatureSoundsPlaying) {
    return null
  }

  return (
    <>
      {/* Nature Sounds Player - handles multiple simultaneous audio streams */}
      <NatureSoundsPlayer />

      <AnimatePresence>
        <motion.div
        ref={playerRef}
        initial={{ y: 100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        exit={{ y: 100, opacity: 0 }}
        transition={{ duration: 0.3, ease: "easeOut" }}
        className="fixed bottom-0 z-[9999] bg-background/95 backdrop-blur-sm border-t border-border shadow-lg transition-all duration-300 ease-[cubic-bezier(0.4,0,0.2,1)]"
        style={{
          left: isMobile ? '0' : (sidebarState === "collapsed" ? 'var(--sidebar-width-icon, 3rem)' : 'var(--sidebar-width, 16rem)'),
          right: '0',
        }}
      >
        <div className="w-full p-4">
          {/* Music Section */}
          {globalPlayer.currentTrack && (
            <div className={cn(
              globalPlayer.isPlaylistMode && globalPlayer.areNatureSoundsPlaying && globalPlayer.natureSounds.length > 0
                ? "mb-4 pb-4 border-b border-border"
                : "mb-0 pb-0"
            )}>
              <div className="flex items-center gap-4">
                {/* Music Play/Pause Button */}
                <Button
                  variant="ghost"
                  size="icon"
                  className={cn(
                    "h-10 w-10 rounded-full shrink-0 transition-all duration-200",
                    isPlaying
                      ? "bg-gradient-to-r from-orange-500 to-rose-500 text-white hover:from-orange-600 hover:to-rose-600"
                      : "hover:bg-muted"
                  )}
                  onClick={handlePlayPause}
                >
                  {isPlaying ? (
                    <Pause className="h-4 w-4" />
                  ) : (
                    <Play className="h-4 w-4 ml-0.5" />
                  )}
                </Button>

                {/* Music Track Icon */}
                <div className="h-10 w-10 rounded-lg bg-gradient-to-br from-orange-100 to-rose-100 dark:from-orange-900/30 dark:to-rose-900/30 flex items-center justify-center shrink-0">
                  <Music2 className="h-4 w-4 text-orange-600 dark:text-orange-400" />
                </div>

                {/* Music Track Info */}
                <div className="flex-1 min-w-0">
                  <div className="truncate font-medium text-sm">
                    {globalPlayer.currentTrack.title}
                  </div>
                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    {globalPlayer.currentTrack.type === "music" && globalPlayer.currentTrack.genres && globalPlayer.currentTrack.genres.length > 0 && (
                      <>
                        <Badge variant="secondary" className="text-xs">
                          {globalPlayer.currentTrack.genres[0]}
                        </Badge>
                        <span>•</span>
                      </>
                    )}
                    <span className="text-muted-foreground">
                      {globalPlayer.isPlaylistMode ? "Playlist" : "Music Track"}
                    </span>
                  </div>
                </div>

                {/* Music Progress Bar with Time Display */}
                <div className="hidden md:flex flex-1 max-w-80 items-center justify-center gap-3">
                  <span className="text-xs text-muted-foreground font-mono min-w-[2.5rem] text-right">
                    {formatTime(currentTime)}
                  </span>
                  <Slider
                    value={[currentTime]}
                    min={0}
                    max={duration || 100}
                    step={0.1}
                    onValueChange={handleSeek}
                    className="cursor-pointer flex-1"
                  />
                  <span className="text-xs text-muted-foreground font-mono min-w-[2.5rem] text-left">
                    {formatTime(duration)}
                  </span>
                </div>

                {/* Music Volume Controls */}
                <div className="flex items-center gap-2 shrink-0">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8"
                    onClick={handleMusicVolumeToggle}
                  >
                    {globalPlayer.musicIsMuted ? (
                      <VolumeX className="h-4 w-4" />
                    ) : (
                      <Volume2 className="h-4 w-4" />
                    )}
                  </Button>
                  <Slider
                    value={[globalPlayer.musicIsMuted ? 0 : globalPlayer.musicVolume]}
                    min={0}
                    max={100}
                    step={1}
                    onValueChange={handleMusicVolumeChange}
                    className="w-20"
                  />
                </div>

                {/* Close Button - Red themed for destructive action */}
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 shrink-0 hover:bg-red-100 hover:text-red-600 dark:hover:bg-red-900/30 dark:hover:text-red-400 text-muted-foreground transition-colors"
                  onClick={handleClose}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>

              {/* Mobile Music Progress Bar */}
              <div className="md:hidden mt-3">
                <div className="flex items-center justify-center gap-3">
                  <span className="text-xs text-muted-foreground font-mono">
                    {formatTime(currentTime)}
                  </span>
                  <Slider
                    value={[currentTime]}
                    min={0}
                    max={duration || 100}
                    step={0.1}
                    onValueChange={handleSeek}
                    className="cursor-pointer flex-1"
                  />
                  <span className="text-xs text-muted-foreground font-mono">
                    {formatTime(duration)}
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* Nature Sounds Section - Only show in playlist mode */}
          {globalPlayer.isPlaylistMode && globalPlayer.areNatureSoundsPlaying && globalPlayer.natureSounds.length > 0 && (
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Waves className="h-4 w-4 text-emerald-600 dark:text-emerald-400" />
                <span className="text-sm font-medium text-emerald-700 dark:text-emerald-300">
                  Nature Sounds ({globalPlayer.natureSounds.length})
                </span>
              </div>

              <div className="grid gap-2 md:grid-cols-2 lg:grid-cols-3">
                {globalPlayer.natureSounds.map((sound) => (
                  <div key={sound.id} className="flex items-center gap-2 p-2 rounded-lg bg-muted/50">
                    {/* Nature Sound Play/Pause */}
                    <Button
                      variant="ghost"
                      size="icon"
                      className={cn(
                        "h-6 w-6 shrink-0 transition-all duration-200",
                        sound.isPlaying
                          ? "bg-gradient-to-r from-green-500 to-emerald-500 text-white hover:from-green-600 hover:to-emerald-600"
                          : "hover:bg-green-100 hover:text-green-600 dark:hover:bg-green-900/30 dark:hover:text-green-400"
                      )}
                      onClick={() => toggleNatureSound(sound.id)}
                    >
                      {sound.isPlaying ? (
                        <Pause className="h-3 w-3" />
                      ) : (
                        <Play className="h-3 w-3" />
                      )}
                    </Button>

                    {/* Nature Sound Info */}
                    <div className="flex-1 min-w-0">
                      <div className="text-xs font-medium truncate">
                        {sound.title}
                      </div>
                      {sound.isPlaying && (
                        <div className="text-xs text-emerald-600 dark:text-emerald-400">
                          Looping
                        </div>
                      )}
                    </div>

                    {/* Nature Sound Volume */}
                    <div className="flex items-center gap-1 shrink-0">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6 hover:bg-green-100 hover:text-green-600 dark:hover:bg-green-900/30 dark:hover:text-green-400 transition-colors"
                        onClick={() => toggleNatureSoundMute(sound.id)}
                      >
                        {sound.isMuted ? (
                          <VolumeX className="h-3 w-3 text-green-600 dark:text-green-400" />
                        ) : (
                          <Volume2 className="h-3 w-3 text-green-600 dark:text-green-400" />
                        )}
                      </Button>
                      <Slider
                        value={[sound.isMuted ? 0 : sound.volume]}
                        min={0}
                        max={100}
                        step={1}
                        onValueChange={(value) => {
                          setNatureSoundVolume(sound.id, value[0])
                          if (value[0] > 0 && sound.isMuted) {
                            toggleNatureSoundMute(sound.id)
                          }
                        }}
                        className="w-16 [&_[data-slot=slider-range]]:bg-green-500 dark:[&_[data-slot=slider-range]]:bg-green-400 [&_[data-slot=slider-track]]:bg-green-200 dark:[&_[data-slot=slider-track]]:bg-green-900/30 [&_[data-slot=slider-thumb]]:bg-green-500 [&_[data-slot=slider-thumb]]:border-green-500 dark:[&_[data-slot=slider-thumb]]:bg-green-400 dark:[&_[data-slot=slider-thumb]]:border-green-400"
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}


        </div>
      </motion.div>
    </AnimatePresence>
    </>
  )
}
