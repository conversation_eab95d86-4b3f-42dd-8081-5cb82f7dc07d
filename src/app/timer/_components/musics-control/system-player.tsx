'use client';

import React, { useEffect } from 'react';
import { useGlobalSystemPlayer } from './use-global-system-player';
import { MainPlayer } from './main-player';
import { NatureSounds } from './nature-sounds';
import { MusicControlProps } from './types';
import { Music } from 'lucide-react';

// Type guards to check playlist types
function hasMusicPlaylist(playlist: MusicControlProps['playlist']): playlist is {
  id: string;
  name: string;
  musics: {
    id: string;
    title: string;
    src?: string | null;
    createdAt?: string;
    updatedAt?: string;
    isPublic?: boolean;
    creatorType?: string;
    userId?: string;
    genres?: string[];
  }[];
  [key: string]: any;
} {
  return playlist !== null && 
         typeof playlist === 'object' && 
         'musics' in playlist && 
         Array.isArray(playlist.musics) && 
         playlist.musics.length > 0;
}

function hasNatureSoundsPlaylist(playlist: MusicControlProps['playlist']): playlist is {
  id: string;
  name: string;
  natureSounds: {
    id: string;
    title: string;
    src?: string | null;
    category?: string[];
  }[];
  [key: string]: any;
} {
  return playlist !== null && 
         typeof playlist === 'object' && 
         'natureSounds' in playlist && 
         Array.isArray(playlist.natureSounds) && 
         playlist.natureSounds.length > 0;
}

interface SystemPlayerProps {
  playlist: MusicControlProps['playlist'];
  className?: string;
  onPlayerRef?: (ref: any) => void;
}

export function SystemPlayer({ playlist, className, onPlayerRef }: SystemPlayerProps) {
  // Use the new global system player hook
  const {
    currentMusicIndex,
    isMainPlaying,
    mainVolume,
    isMainMuted,
    playbackMode,
    togglePlaybackMode,
    toggleMainPlay,
    skipToNext,
    skipToPrevious,
    toggleMainMute,
    handleMainVolumeChange,
    currentTime,
    duration,
    handleSeek,
    natureSounds,
    toggleNatureSound,
    handleNatureSoundVolume,
    toggleNatureSoundMute,
    playingNatureSoundsCount,
    pauseMainOnly,
    pauseAll,
    canPlay,
  } = useGlobalSystemPlayer(playlist);

  // Provide pause functions to parent
  useEffect(() => {
    if (onPlayerRef) {
      onPlayerRef({ pauseMainOnly, pauseAll });
    }
  }, [onPlayerRef, pauseMainOnly, pauseAll]);

  // The global system player hook now handles all initialization and cleanup

  // Current track info
  const currentTrack = hasMusicPlaylist(playlist) ? playlist.musics[currentMusicIndex] : undefined;
  const musicCount = hasMusicPlaylist(playlist) ? playlist.musics.length : 0;

  // Check if we have any playlist data at all
  const hasAnyPlaylist = hasMusicPlaylist(playlist) || hasNatureSoundsPlaylist(playlist);

  return (
    <div className={`space-y-4 max-h-full overflow-y-auto ${className}`}>
      {/* Show placeholder message when no playlist is available */}
      {!hasAnyPlaylist ? (
        <div className="space-y-4">
          {/* Placeholder message */}
          <div className="bg-card/20 backdrop-blur-sm rounded-xl p-6 shadow-sm border border-border/30 text-center">
            <div className="flex flex-col items-center gap-3">
              <div className="h-12 w-12 bg-muted/50 rounded-lg flex items-center justify-center">
                <Music className="h-6 w-6 text-muted-foreground/50" />
              </div>
              <div>
                <h3 className="font-medium text-foreground mb-1">No System Playlist</h3>
                <p className="text-sm text-muted-foreground leading-relaxed max-w-sm">
                  This video doesn't have any music or nature sounds attached. Try the YouTube or Spotify tabs for streaming music instead.
                </p>
              </div>
            </div>
          </div>
          
          {/* Disabled player interface for consistency */}
          <MainPlayer
            currentTrackTitle="No tracks available"
            playlistName="No playlist attached"
            currentIndex={0}
            tracksTotal={0}
            isPlaying={false}
            volume={mainVolume}
            isMuted={isMainMuted}
            playbackMode={playbackMode}
            onTogglePlay={() => {}}
            onSkipNext={() => {}}
            onSkipPrevious={() => {}}
            onToggleMute={() => {}}
            onTogglePlaybackMode={() => {}}
            onVolumeChange={() => {}}
            currentTime={0}
            duration={0}
            onSeek={() => {}}
            disabled={true}
          />
        </div>
      ) : (
        <>
          {/* Main Music Player */}
          <MainPlayer
            currentTrackTitle={currentTrack?.title}
            playlistName={playlist?.name}
            currentIndex={currentMusicIndex}
            tracksTotal={musicCount}
            isPlaying={isMainPlaying}
            volume={mainVolume}
            isMuted={isMainMuted}
            playbackMode={playbackMode}
            onTogglePlay={toggleMainPlay}
            onSkipNext={skipToNext}
            onSkipPrevious={skipToPrevious}
            onToggleMute={toggleMainMute}
            onTogglePlaybackMode={togglePlaybackMode}
            onVolumeChange={handleMainVolumeChange}
            currentTime={currentTime}
            duration={duration}
            onSeek={handleSeek}
            disabled={!hasMusicPlaylist(playlist) || !canPlay}
          />

          {/* Nature Sounds Section */}
          <NatureSounds 
            natureSounds={natureSounds}
            playingCount={playingNatureSoundsCount}
            onToggleSound={toggleNatureSound}
            onVolumeChange={handleNatureSoundVolume}
            onToggleMute={toggleNatureSoundMute}
          />
        </>
      )}
    </div>
  );
} 