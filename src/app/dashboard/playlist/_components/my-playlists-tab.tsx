"use client"

import { useState, useMemo } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Plus, Music2, Grid3X3, List, Clock, Heart, Play } from "lucide-react"
import { Skeleton } from "@/components/ui/skeleton"
import { useGetMusicPlaylistsUser } from "@schemas/MusicPlaylist/music-playlist-user-query"
import { PlaylistCard } from "./playlist-card"
import { PlaylistFormSheet } from "./playlist-form-sheet"
import { AnimatePresence, motion } from "framer-motion"
import { cn } from "@/lib/utils"

import { Card } from "@/components/ui/card"

export function MyPlaylistsTab() {
  const [isCreateSheetOpen, setIsCreateSheetOpen] = useState(false)
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")

  // Get user's playlists
  const { data: playlists, isLoading } = useGetMusicPlaylistsUser()

  // Sort playlists by newest first (default)
  const sortedPlaylists = useMemo(() => {
    if (!playlists) return []

    return [...playlists].sort((a, b) => {
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    })
  }, [playlists])



  // Enhanced stats with more insights
  const stats = useMemo(() => {
    if (!playlists) return { total: 0, items: 0, totalDuration: 0, avgItems: 0 }
    
    const total = playlists.length
    const items = playlists.reduce((acc, playlist) => 
      acc + (playlist.videos?.length || 0) + (playlist.musics?.length || 0), 0)
    
    return {
      total,
      items,
      totalDuration: Math.floor(items * 3.5), // Estimated duration in minutes
      avgItems: total > 0 ? Math.round(items / total) : 0
    }
  }, [playlists])

  // Compact Loading State
  if (isLoading) {
    return (
      <div
        className="space-y-6"
        style={{
          paddingBottom: 'var(--global-audio-player-height, 0px)'
        }}
      >
        {/* Compact Header skeleton */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Skeleton className="h-10 w-10 rounded-xl" />
              <div className="space-y-2">
                <Skeleton className="h-6 w-48" />
                <Skeleton className="h-4 w-64" />
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Skeleton className="h-10 w-32" />
              <Skeleton className="h-10 w-16" />
            </div>
          </div>
          
          {/* Compact stats skeleton */}
          <div className="flex gap-6">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="flex items-center gap-2">
                <Skeleton className="h-4 w-4 rounded" />
                <div className="space-y-1">
                  <Skeleton className="h-5 w-8" />
                  <Skeleton className="h-3 w-12" />
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Grid skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4">
          {Array.from({ length: 10 }).map((_, i) => (
            <Card key={i} className="overflow-hidden">
              <div className="space-y-3">
                <Skeleton className="aspect-[4/3] w-full" />
                <div className="p-4 space-y-2">
                  <Skeleton className="h-5 w-3/4" />
                  <Skeleton className="h-4 w-full" />
                  <div className="flex gap-2">
                    <Skeleton className="h-3 w-12" />
                    <Skeleton className="h-3 w-12" />
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div
      className="space-y-6"
      style={{
        paddingBottom: 'var(--global-audio-player-height, 0px)'
      }}
    >
      {/* Compact Header with Inline Stats */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-xl bg-gradient-to-br from-orange-500/10 to-rose-500/10 border border-orange-200/20 dark:border-orange-700/20">
              <Music2 className="h-5 w-5 text-orange-600 dark:text-orange-400" />
            </div>
            <div>
              <h1 className="text-xl font-bold bg-gradient-to-r from-orange-500 via-red-500 to-rose-600 bg-clip-text text-transparent">
                My Music Playlists
              </h1>
              <p className="text-sm text-muted-foreground">
                Curate your perfect focus soundscape
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <Button
              onClick={() => setIsCreateSheetOpen(true)}
              className="bg-gradient-to-r from-orange-500 via-red-500 to-rose-600 hover:from-orange-600 hover:via-red-600 hover:to-rose-700 text-white shadow-lg hover:shadow-xl transition-all duration-200 group"
            >
              <Plus className="mr-2 h-4 w-4 group-hover:rotate-90 transition-transform duration-200" />
              Create
            </Button>

            {/* View mode toggle - Only show when playlists exist */}
            {sortedPlaylists.length > 0 && (
              <div className="flex rounded-md border border-border p-0.5 bg-background">
                <Button
                  variant={viewMode === "grid" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("grid")}
                  className={cn(
                    "h-8 w-8 p-0 transition-all duration-200",
                    viewMode === "grid" && "bg-primary text-primary-foreground shadow-sm"
                  )}
                >
                  <Grid3X3 className="h-3.5 w-3.5" />
                </Button>
                <Button
                  variant={viewMode === "list" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("list")}
                  className={cn(
                    "h-8 w-8 p-0 transition-all duration-200",
                    viewMode === "list" && "bg-primary text-primary-foreground shadow-sm"
                  )}
                >
                  <List className="h-3.5 w-3.5" />
                </Button>
              </div>
            )}
          </div>
        </div>

        {/* Compact Inline Stats - Only show when playlists exist */}
        {sortedPlaylists.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="flex items-center gap-6 py-2 px-4 rounded-lg bg-muted/30 border border-border/50"
          >
            <div className="flex items-center gap-2">
              <div className="p-1.5 rounded-md bg-blue-500/10">
                <Music2 className="h-3.5 w-3.5 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="text-sm">
                <span className="font-semibold text-blue-700 dark:text-blue-300">{stats.total}</span>
                <span className="text-muted-foreground ml-1">playlists</span>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <div className="p-1.5 rounded-md bg-green-500/10">
                <Play className="h-3.5 w-3.5 text-green-600 dark:text-green-400" />
              </div>
              <div className="text-sm">
                <span className="font-semibold text-green-700 dark:text-green-300">{stats.items}</span>
                <span className="text-muted-foreground ml-1">tracks</span>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <div className="p-1.5 rounded-md bg-orange-500/10">
                <Clock className="h-3.5 w-3.5 text-orange-600 dark:text-orange-400" />
              </div>
              <div className="text-sm">
                <span className="font-semibold text-orange-700 dark:text-orange-300">{(stats.totalDuration / 60).toFixed(1)}h</span>
                <span className="text-muted-foreground ml-1">duration</span>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <div className="p-1.5 rounded-md bg-rose-500/10">
                <Heart className="h-3.5 w-3.5 text-rose-600 dark:text-rose-400" />
              </div>
              <div className="text-sm">
                <span className="font-semibold text-rose-700 dark:text-rose-300">{stats.avgItems}</span>
                <span className="text-muted-foreground ml-1">avg/list</span>
              </div>
            </div>
          </motion.div>
        )}
      </div>

      {/* Enhanced Playlists Display */}
      <AnimatePresence mode="wait">
        {sortedPlaylists.length === 0 ? (
          <motion.div
            key="empty"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.3 }}
            className="flex flex-col items-center justify-center py-20 text-center"
          >
            {/* Enhanced Icon with Animation */}
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 0.1, type: "spring", stiffness: 200, damping: 15 }}
              className="relative mb-8"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-orange-400/20 to-rose-400/20 rounded-full blur-xl scale-150" />
              <div className="relative p-6 rounded-2xl bg-gradient-to-br from-orange-50 via-red-50 to-rose-50 dark:from-orange-950/50 dark:via-red-950/30 dark:to-rose-950/50 border border-orange-200/50 dark:border-orange-800/50 shadow-lg">
                <motion.div
                  animate={{
                    rotate: [0, -10, 10, -5, 5, 0],
                    scale: [1, 1.05, 1]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    repeatDelay: 3,
                    ease: "easeInOut"
                  }}
                >
                  <Music2 className="h-16 w-16 text-orange-500 dark:text-orange-400" />
                </motion.div>
              </div>
            </motion.div>

            {/* Enhanced Content */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.5 }}
              className="space-y-6 max-w-lg"
            >
              <div className="space-y-3">
                <h3 className="text-2xl font-bold bg-gradient-to-r from-orange-600 via-red-600 to-rose-600 bg-clip-text text-transparent">
                  Your music journey starts here
                </h3>
                <p className="text-base text-muted-foreground leading-relaxed px-4">
                  Create your first playlist to organize your focus music and enhance your productivity sessions.
                </p>
              </div>

              {/* Enhanced Features List */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.5, duration: 0.5 }}
                className="grid grid-cols-1 sm:grid-cols-3 gap-4 py-4"
              >
                <div className="flex flex-col items-center gap-2 p-3 rounded-lg bg-muted/30">
                  <div className="p-2 rounded-full bg-blue-500/10">
                    <Music2 className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                  </div>
                  <span className="text-xs font-medium text-muted-foreground">Organize Music</span>
                </div>
                <div className="flex flex-col items-center gap-2 p-3 rounded-lg bg-muted/30">
                  <div className="p-2 rounded-full bg-green-500/10">
                    <Play className="h-4 w-4 text-green-600 dark:text-green-400" />
                  </div>
                  <span className="text-xs font-medium text-muted-foreground">Focus Sessions</span>
                </div>
                <div className="flex flex-col items-center gap-2 p-3 rounded-lg bg-muted/30">
                  <div className="p-2 rounded-full bg-orange-500/10">
                    <Heart className="h-4 w-4 text-orange-600 dark:text-orange-400" />
                  </div>
                  <span className="text-xs font-medium text-muted-foreground">Boost Productivity</span>
                </div>
              </motion.div>

              {/* Centered Button */}
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.7, duration: 0.3 }}
                className="flex justify-center pt-2"
              >
                <Button
                  onClick={() => setIsCreateSheetOpen(true)}
                  size="lg"
                  className="bg-gradient-to-r from-orange-500 via-red-500 to-rose-600 hover:from-orange-600 hover:via-red-600 hover:to-rose-700 text-white shadow-lg hover:shadow-xl transition-all duration-200 group px-8"
                >
                  <Plus className="mr-2 h-5 w-5 group-hover:rotate-90 transition-transform duration-200" />
                  Create First Playlist
                </Button>
              </motion.div>
            </motion.div>
          </motion.div>
        ) : (
          <motion.div
            key="playlists"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className={cn(
              "transition-all duration-200",
              viewMode === "grid" 
                ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4"
                : "space-y-2"
            )}
          >
            {sortedPlaylists.map((playlist, index) => (
              <motion.div
                key={playlist.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ 
                  delay: index * 0.03, 
                  duration: 0.2,
                  ease: "easeOut"
                }}
                layout
              >
                <PlaylistCard playlist={playlist} viewMode={viewMode} />
              </motion.div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Create Playlist Sheet */}
      <PlaylistFormSheet 
        isOpen={isCreateSheetOpen} 
        onOpenChange={setIsCreateSheetOpen}
      />
    </div>
  )
}