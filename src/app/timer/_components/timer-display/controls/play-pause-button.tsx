import { But<PERSON> } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { Play, Pause, RotateCcw } from 'lucide-react';
import { memo } from 'react';
import { PlayPauseButtonProps } from '../common/types';
import { TimerColorPreset } from '@/lib/pomodoro-store';

const PlayPauseButton = memo(({
  isRunning,
  onClick,
  onReset,
  timerColor
}: PlayPauseButtonProps) => {
  // If there's no reset button or it's not running (so reset is invisible)
  const shouldCenter = !onReset || !isRunning;

  // Enhanced color classes for glass backgrounds - improved contrast and readability
  const getColorClasses = (color: TimerColorPreset) => {
    switch (color) {
      case 'white':
        return {
          text: "text-white",
          hoverBg: "hover:bg-white/10"
        };
      case 'blue':
        return {
          text: "text-blue-100", // Enhanced brightness for glass
          hoverBg: "hover:bg-blue-500/20"
        };
      case 'orange':
        return {
          text: "text-orange-100", // Enhanced brightness for glass
          hoverBg: "hover:bg-orange-500/20"
        };
      case 'purple':
        return {
          text: "text-purple-100", // Enhanced brightness for glass
          hoverBg: "hover:bg-purple-500/20"
        };
      case 'green':
        return {
          text: "text-green-100", // Enhanced brightness for glass
          hoverBg: "hover:bg-green-500/20"
        };
      case 'red':
        return {
          text: "text-red-100", // Enhanced brightness for glass
          hoverBg: "hover:bg-red-500/20"
        };
      case 'yellow':
        return {
          text: "text-yellow-100", // Enhanced brightness for glass
          hoverBg: "hover:bg-yellow-500/20"
        };
      case 'indigo':
        return {
          text: "text-indigo-100", // Enhanced brightness for glass
          hoverBg: "hover:bg-indigo-500/20"
        };
      case 'pink':
        return {
          text: "text-pink-100", // Enhanced brightness for glass
          hoverBg: "hover:bg-pink-500/20"
        };
      default:
        return {
          text: "text-white",
          hoverBg: "hover:bg-white/10"
        };
    }
  };

  const colorClasses = getColorClasses(timerColor);

  return (
    <div className={cn(
      "flex",
      shouldCenter ? "justify-center w-full" : "items-center gap-2"
    )}>
      <Button
        variant="ghost"
        size="sm"
        className={cn(
          "h-8 px-3 transition-all duration-200 ease-out rounded-xl cursor-pointer",
          colorClasses.text,
          "bg-black/20 backdrop-blur-sm",
          "border border-white/10 hover:border-white/20",
          colorClasses.hoverBg,
          "active:scale-[0.98]",
          isRunning ? "ring-1 ring-white/10" : "",
          // Override any shadowing or white effects from the Button component
          "shadow-none hover:shadow-none",
          "hover:text-white focus:text-white" // Ensure text stays white on all states
        )}
        onClick={onClick}
        title={isRunning ? "Pause timer" : "Start timer"}
        aria-label={isRunning ? "Pause timer" : "Start timer"}
        style={{
          // Enhanced styling for glass backgrounds
          boxShadow: 'none',
          textShadow: '0 1px 3px rgba(0, 0, 0, 0.5), 0 1px 2px rgba(0, 0, 0, 0.3)', // Enhanced text shadow for glass readability
        }}
      >
        <div className="flex items-center gap-1.5 z-10">
          {isRunning ?
            <Pause className="h-3.5 w-3.5" /> :
            <Play className="h-3.5 w-3.5 ml-0.5" />
          }
          <span className="text-xs font-medium">{isRunning ? "Pause" : "Start"}</span>
        </div>
      </Button>

      {onReset && isRunning && (
        <Button
          variant="ghost"
          size="icon"
          className={cn(
            "h-8 w-8 transition-all duration-200 ease-out rounded-xl cursor-pointer",
            "bg-black/20 backdrop-blur-sm",
            "border border-white/10 hover:border-white/20",
            colorClasses.text,
            colorClasses.hoverBg,
            "active:scale-[0.98]",
            "ml-0",
            // Override any shadowing or white effects
            "shadow-none hover:shadow-none",
            "hover:text-white focus:text-white" // Ensure text stays white on all states
          )}
          onClick={onReset}
          title="Reset timer"
          aria-label="Reset timer"
          style={{
            boxShadow: 'none',
            textShadow: '0 1px 3px rgba(0, 0, 0, 0.5), 0 1px 2px rgba(0, 0, 0, 0.3)', // Enhanced text shadow for glass readability
          }}
        >
          <RotateCcw className="h-3.5 w-3.5" />
        </Button>
      )}
    </div>
  );
});
PlayPauseButton.displayName = 'PlayPauseButton';

export default PlayPauseButton;