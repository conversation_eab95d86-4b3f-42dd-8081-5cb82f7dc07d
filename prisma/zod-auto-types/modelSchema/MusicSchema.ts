import { z } from 'zod';
import { MediaSourceSchema } from '../inputTypeSchemas/MediaSourceSchema'
import { UserRoleSchema } from '../inputTypeSchemas/UserRoleSchema'
import { MusicGenreSchema } from '../inputTypeSchemas/MusicGenreSchema'
import { UserWithRelationsSchema, UserPartialWithRelationsSchema } from './UserSchema'
import type { UserWithRelations, UserPartialWithRelations } from './UserSchema'
import { MusicPlaylistWithRelationsSchema, MusicPlaylistPartialWithRelationsSchema } from './MusicPlaylistSchema'
import type { MusicPlaylistWithRelations, MusicPlaylistPartialWithRelations } from './MusicPlaylistSchema'
import { MusicPlaylistUserWithRelationsSchema, MusicPlaylistUserPartialWithRelationsSchema } from './MusicPlaylistUserSchema'
import type { MusicPlaylistUserWithRelations, MusicPlaylistUserPartialWithRelations } from './MusicPlaylistUserSchema'

/////////////////////////////////////////
// MUSIC SCHEMA
/////////////////////////////////////////

export const MusicSchema = z.object({
  source: MediaSourceSchema.nullish(),
  creatorType: UserRoleSchema,
  genres: MusicGenreSchema.array(),
  id: z.string().cuid(),
  title: z.string().min(1),
  src: z.string().url().nullish(),
  rating: z.number().min(0).max(5).nullish(),
  isPublic: z.boolean(),
  isCopyright: z.boolean(),
  duration: z.number().int().nullish(),
  note: z.string().nullish(),
  useAsOpeningMusic: z.boolean(),
  userId: z.string(),
  createdAt: z.union([z.date(), z.string().datetime()]),
  updatedAt: z.union([z.date(), z.string().datetime()]),
})

export type Music = z.infer<typeof MusicSchema>

/////////////////////////////////////////
// MUSIC PARTIAL SCHEMA
/////////////////////////////////////////

export const MusicPartialSchema = MusicSchema.partial()

export type MusicPartial = z.infer<typeof MusicPartialSchema>

/////////////////////////////////////////
// MUSIC RELATION SCHEMA
/////////////////////////////////////////

export type MusicRelations = {
  user: UserWithRelations;
  musicPlaylists: MusicPlaylistWithRelations[];
  musicPlaylistsUser: MusicPlaylistUserWithRelations[];
};

export type MusicWithRelations = z.infer<typeof MusicSchema> & MusicRelations

export const MusicWithRelationsSchema: z.ZodType<MusicWithRelations> = MusicSchema.merge(z.object({
  user: z.lazy(() => UserWithRelationsSchema),
  musicPlaylists: z.lazy(() => MusicPlaylistWithRelationsSchema).array(),
  musicPlaylistsUser: z.lazy(() => MusicPlaylistUserWithRelationsSchema).array(),
}))

/////////////////////////////////////////
// MUSIC PARTIAL RELATION SCHEMA
/////////////////////////////////////////

export type MusicPartialRelations = {
  user?: UserPartialWithRelations;
  musicPlaylists?: MusicPlaylistPartialWithRelations[];
  musicPlaylistsUser?: MusicPlaylistUserPartialWithRelations[];
};

export type MusicPartialWithRelations = z.infer<typeof MusicPartialSchema> & MusicPartialRelations

export const MusicPartialWithRelationsSchema: z.ZodType<MusicPartialWithRelations> = MusicPartialSchema.merge(z.object({
  user: z.lazy(() => UserPartialWithRelationsSchema),
  musicPlaylists: z.lazy(() => MusicPlaylistPartialWithRelationsSchema).array(),
  musicPlaylistsUser: z.lazy(() => MusicPlaylistUserPartialWithRelationsSchema).array(),
})).partial()

export type MusicWithPartialRelations = z.infer<typeof MusicSchema> & MusicPartialRelations

export const MusicWithPartialRelationsSchema: z.ZodType<MusicWithPartialRelations> = MusicSchema.merge(z.object({
  user: z.lazy(() => UserPartialWithRelationsSchema),
  musicPlaylists: z.lazy(() => MusicPlaylistPartialWithRelationsSchema).array(),
  musicPlaylistsUser: z.lazy(() => MusicPlaylistUserPartialWithRelationsSchema).array(),
}).partial())

export default MusicSchema;
