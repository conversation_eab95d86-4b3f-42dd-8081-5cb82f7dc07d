import { z } from "zod";
import { VideoSchema, VideoPartialSchema } from "@types";
import { VideoGenreSchema } from "@types";

export const createVideoSchema = VideoSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  userId: true,
  creatorType: true,
  order: true,
}).extend({
  thumbnail: z
    .string()
    .transform((value) => {
      // Handle empty strings, "undefined" strings, and null/undefined values
      if (!value || value === "" || value === "undefined" || value === "null") {
        return undefined;
      }
      return value;
    })
    .optional()
    .refine((value) => {
      // Only validate as URL if value exists and is not empty
      if (!value) return true;
      try {
        new URL(value);
        return true;
      } catch {
        return false;
      }
    }, {
      message: "Please enter a valid URL"
    }),
  isPremium: z.union([
    z.boolean(),
    z.string().transform(val => val === "true")
  ]).optional().default(false),
  isPublic: z.union([
    z.boolean(),
    z.string().transform(val => val === "true")
  ]).optional().default(true),
  description: z.string().optional(),
  musicPlaylistId: z.string().optional().nullable(),
  naturePlaylistId: z.string().optional().nullable(),
  videoGenre: VideoGenreSchema["array"]().optional(),
});

export const updateVideoSchema = VideoPartialSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  userId: true,
  creatorType: true,
}).extend({
  thumbnail: z
    .string()
    .transform((value) => {
      // Handle empty strings, "undefined" strings, and null/undefined values
      if (!value || value === "" || value === "undefined" || value === "null") {
        return undefined;
      }
      return value;
    })
    .optional()
    .refine((value) => {
      // Only validate as URL if value exists and is not empty
      if (!value) return true;
      try {
        new URL(value);
        return true;
      } catch {
        return false;
      }
    }, {
      message: "Please enter a valid URL"
    }),
  isPremium: z.union([
    z.boolean(),
    z.string().transform(val => val === "true")
  ]).optional(),
  isPublic: z.union([
    z.boolean(),
    z.string().transform(val => val === "true")
  ]).optional(),
  description: z.string().optional(),
  musicPlaylistId: z.string().optional().nullable(),
  naturePlaylistId: z.string().optional().nullable(),
  videoGenre: VideoGenreSchema["array"]().optional(),
});

export type CreateVideoInput = z.infer<typeof createVideoSchema>;
export type UpdateVideoInput = z.infer<typeof updateVideoSchema>; 