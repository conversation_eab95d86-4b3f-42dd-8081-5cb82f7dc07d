"use client"

import * as React from "react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { cn } from "@/lib/utils"

interface ConfirmationDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  title: string
  description: string
  confirmText?: string
  cancelText?: string
  onConfirm: () => void | Promise<void>
  isLoading?: boolean
  variant?: "destructive" | "default"
}

export function ConfirmationDialog({
  open,
  onOpenChange,
  title,
  description,
  confirmText = "Delete",
  cancelText = "Cancel",
  onConfirm,
  isLoading = false,
  variant = "destructive"
}: ConfirmationDialogProps) {
  const handleConfirm = async () => {
    try {
      await onConfirm()
    } catch (error) {
      // Error handling should be done in the parent component
      console.error("Confirmation action failed:", error)
    }
  }

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent 
        className={cn(
          // Compact glassmorphism styling with 15-20px border-radius
          "rounded-[18px] p-4 sm:p-5 max-w-[calc(100%-2rem)] sm:max-w-md",
          // Glassmorphism background
          "bg-background/95 backdrop-blur-md",
          "border border-white/10",
          // Enhanced shadows for glass effect
          "shadow-2xl shadow-black/20",
          // Inset shadows for glass depth
          "before:absolute before:inset-0 before:rounded-[18px] before:bg-gradient-to-br before:from-white/10 before:to-transparent before:pointer-events-none",
          // Glass reflection overlay
          "after:absolute after:inset-0 after:rounded-[18px] after:bg-gradient-to-br after:from-transparent after:via-white/5 after:to-transparent after:pointer-events-none"
        )}
      >
        <AlertDialogHeader className="space-y-2 text-center sm:text-left">
          <AlertDialogTitle className="text-lg font-semibold">
            {title}
          </AlertDialogTitle>
          <AlertDialogDescription className="text-sm text-muted-foreground leading-relaxed">
            {description}
          </AlertDialogDescription>
        </AlertDialogHeader>
        
        <AlertDialogFooter className="flex flex-col-reverse gap-2 sm:flex-row sm:justify-end mt-4">
          <AlertDialogCancel 
            disabled={isLoading}
            className="sm:w-auto"
          >
            {cancelText}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            disabled={isLoading}
            className={cn(
              "sm:w-auto",
              variant === "destructive" && [
                // Red color scheme for delete actions
                "bg-red-600 text-white hover:bg-red-700 focus:bg-red-700",
                "border-red-600 hover:border-red-700",
                // Remove active white border effects
                "focus:outline-none focus:ring-2 focus:ring-red-500/20 focus:ring-offset-0",
                // Subtle hover effect with 150-200ms transition
                "transition-all duration-150 ease-out",
                "hover:shadow-lg hover:shadow-red-500/25"
              ]
            )}
          >
            {isLoading ? "Processing..." : confirmText}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}

// Hook for easier usage
export function useConfirmationDialog() {
  const [isOpen, setIsOpen] = React.useState(false)
  const [config, setConfig] = React.useState<Omit<ConfirmationDialogProps, 'open' | 'onOpenChange'> | null>(null)

  const openDialog = React.useCallback((dialogConfig: Omit<ConfirmationDialogProps, 'open' | 'onOpenChange'>) => {
    setConfig(dialogConfig)
    setIsOpen(true)
  }, [])

  const closeDialog = React.useCallback(() => {
    setIsOpen(false)
    // Clear config after animation completes
    setTimeout(() => setConfig(null), 200)
  }, [])

  const ConfirmationDialogComponent = React.useMemo(() => {
    if (!config) return null
    
    return (
      <ConfirmationDialog
        {...config}
        open={isOpen}
        onOpenChange={closeDialog}
      />
    )
  }, [config, isOpen, closeDialog])

  return {
    openDialog,
    closeDialog,
    ConfirmationDialog: ConfirmationDialogComponent
  }
}
