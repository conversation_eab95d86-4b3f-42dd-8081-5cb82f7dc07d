"use client"

import { useEffect, useRef, useMemo } from "react"
import { useAudioStore } from "@/lib/audio-store"

interface NatureSoundsPlayerProps {
  className?: string
}

export function NatureSoundsPlayer({ className }: NatureSoundsPlayerProps) {
  const { globalPlayer } = useAudioStore()
  const audioRefsRef = useRef<Map<string, HTMLAudioElement>>(new Map())

  // Create a stable reference to track previous sound states to minimize re-renders
  const prevSoundsRef = useRef<Map<string, { isPlaying: boolean; volume: number; isMuted: boolean; src: string }>>(new Map())

  // Memoize current sounds state for comparison
  const currentSoundsState = useMemo(() => {
    const state = new Map<string, { isPlaying: boolean; volume: number; isMuted: boolean; src: string }>()
    globalPlayer.natureSounds.forEach(sound => {
      state.set(sound.id, {
        isPlaying: sound.isPlaying,
        volume: sound.volume,
        isMuted: sound.isMuted,
        src: sound.src
      })
    })
    return state
  }, [globalPlayer.natureSounds])

  // Effect to handle overall nature sounds state (start/stop all)
  useEffect(() => {
    if (!globalPlayer.areNatureSoundsPlaying) {
      // Stop all nature sounds
      audioRefsRef.current.forEach(audio => {
        audio.pause()
      })
      audioRefsRef.current.clear()
      prevSoundsRef.current.clear()
      return
    }

    // Remove audio elements for sounds no longer in the list
    const currentSoundIds = new Set(globalPlayer.natureSounds.map(s => s.id))
    audioRefsRef.current.forEach((audio, soundId) => {
      if (!currentSoundIds.has(soundId)) {
        audio.pause()
        audio.src = ''
        audioRefsRef.current.delete(soundId)
        prevSoundsRef.current.delete(soundId)
      }
    })
  }, [globalPlayer.areNatureSoundsPlaying, globalPlayer.natureSounds.length])

  // Effect to handle individual sound changes
  useEffect(() => {
    if (!globalPlayer.areNatureSoundsPlaying || globalPlayer.natureSounds.length === 0) {
      return
    }

    // Process each sound individually, only updating those that have changed
    globalPlayer.natureSounds.forEach(sound => {
      if (!sound.src) return

      const currentState = currentSoundsState.get(sound.id)
      const prevState = prevSoundsRef.current.get(sound.id)

      // Check if this specific sound has changed
      const hasChanged = !prevState ||
        prevState.isPlaying !== currentState?.isPlaying ||
        prevState.volume !== currentState?.volume ||
        prevState.isMuted !== currentState?.isMuted ||
        prevState.src !== currentState?.src

      if (!hasChanged) return // Skip if nothing changed for this sound

      let audio = audioRefsRef.current.get(sound.id)

      if (!audio) {
        // Create new audio element
        audio = new Audio(sound.src)
        audio.loop = true
        audioRefsRef.current.set(sound.id, audio)
      } else if (audio.src !== sound.src) {
        // Update source if changed, but preserve playback position
        const wasPlaying = !audio.paused
        const currentTime = audio.currentTime
        audio.src = sound.src
        audio.load()
        if (wasPlaying && sound.isPlaying) {
          // Try to restore position, but don't worry if it fails
          audio.currentTime = Math.min(currentTime, audio.duration || 0)
          audio.play().catch(error => {
            console.error(`Failed to resume nature sound ${sound.title}:`, error)
          })
        }
      }

      // Update volume
      const targetVolume = sound.isMuted ? 0 : sound.volume / 100
      if (Math.abs(audio.volume - targetVolume) > 0.001) {
        audio.volume = targetVolume
      }

      // Control playback - only change if state doesn't match
      if (sound.isPlaying && audio.paused) {
        audio.play().catch(error => {
          console.error(`Failed to play nature sound ${sound.title}:`, error)
        })
      } else if (!sound.isPlaying && !audio.paused) {
        audio.pause()
      }

      // Update previous state
      if (currentState) {
        prevSoundsRef.current.set(sound.id, { ...currentState })
      }
    })
  }, [currentSoundsState, globalPlayer.areNatureSoundsPlaying])

  // Cleanup effect
  useEffect(() => {
    return () => {
      audioRefsRef.current.forEach(audio => {
        audio.pause()
        audio.src = ''
      })
      audioRefsRef.current.clear()
      prevSoundsRef.current.clear()
    }
  }, [])

  // This component doesn't render anything visible
  return null
}
