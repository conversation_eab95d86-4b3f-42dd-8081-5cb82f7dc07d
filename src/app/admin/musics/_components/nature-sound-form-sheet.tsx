"use client";

import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetD<PERSON><PERSON>,
  SheetFooter
} from "@/components/ui/sheet";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Loader2 } from "lucide-react";
import {
  useCreateNatureSound,
  useUpdateNatureSound,
  useGetNatureSound
} from "@schemas/Natural/nature-sound-query";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { NatureSoundCategory, MediaSource } from "@prisma/client";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

interface NatureSoundFormSheetProps {
  natureSoundId?: string;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export function NatureSoundFormSheet({
  natureSoundId,
  open,
  onOpenChange,
  onSuccess
}: NatureSoundFormSheetProps) {
  const router = useRouter();
  const isEditMode = !!natureSoundId;
  const title = isEditMode ? "Edit Nature Sound" : "Create Nature Sound";

  // Form state
  const [formData, setFormData] = useState({
    title: "",
    src: "",
    source: MediaSource.OTHER as MediaSource,
    categories: [] as NatureSoundCategory[],
    isPublic: true,
    isFeatured: false,
  });

  // Form validation state
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Mutations for creating and updating nature sounds
  const createNatureSoundMutation = useCreateNatureSound();
  const updateNatureSoundMutation = useUpdateNatureSound();

  // For edit mode, fetch the nature sound details
  const natureSoundQuery = useGetNatureSound(natureSoundId);

  // When in edit mode and nature sound data is loaded, populate the form
  useEffect(() => {
    if (isEditMode && natureSoundQuery.data) {
      setFormData({
        title: natureSoundQuery.data.title,
        src: natureSoundQuery.data.src || "",
        source: natureSoundQuery.data.source || MediaSource.OTHER,
        categories: Array.isArray(natureSoundQuery.data.category) ? natureSoundQuery.data.category : (natureSoundQuery.data.category ? [natureSoundQuery.data.category] : []),
        isPublic: natureSoundQuery.data.isPublic,
        isFeatured: (natureSoundQuery.data as any).isFeatured || false,
      });
    }
  }, [isEditMode, natureSoundQuery.data]);

  // Handle input changes
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // Handle switch changes
  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData(prev => ({ ...prev, [name]: checked }));
  };

  // Handle category checkbox changes
  const handleCategoryChange = (category: NatureSoundCategory, checked: boolean) => {
    setFormData(prev => {
      if (checked) {
        return { ...prev, categories: [...prev.categories, category] };
      } else {
        return { ...prev, categories: prev.categories.filter(c => c !== category) };
      }
    });
  };

  // Handle source select changes
  const handleSourceChange = (value: string) => {
    setFormData(prev => ({ ...prev, source: value as MediaSource }));
  };

  // Format source name for display
  const formatSourceName = (source: string) => {
    switch (source) {
      case 'EPIDEMICSOUND':
        return 'Epidemic Sound';
      case 'PIXABAY':
        return 'Pixabay';
      case 'SUNO':
        return 'Suno';
      case 'YOUTUBE':
        return 'YouTube';
      case 'OTHER':
        return 'Other';
      default:
        return source.charAt(0) + source.slice(1).toLowerCase();
    }
  };

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = "Title is required";
    }

    if (formData.src && formData.src.trim()) {
      try {
        new URL(formData.src);
      } catch {
        newErrors.src = "Please enter a valid URL";
      }
    }

    if (formData.categories.length === 0) {
      newErrors.categories = "At least one category is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    // Include the full categories array in the request
    const submitData = {
      title: formData.title,
      src: formData.src || undefined,
      source: formData.source,
      category: formData.categories, // Pass the entire categories array
      isPublic: formData.isPublic.toString(),
      isFeatured: formData.isFeatured.toString(),
    };

    if (isEditMode && natureSoundId) {
      updateNatureSoundMutation.mutate(
        {
          form: submitData,
          param: { id: natureSoundId },
        },
        {
          onSuccess: () => {
            router.refresh();
            onSuccess?.();
            onOpenChange(false);
            toast.success("Nature sound updated successfully");
          },
          onError: (error) => {
            console.error("Error updating nature sound:", error);
            toast.error("Failed to update nature sound");
          }
        }
      );
    } else {
      createNatureSoundMutation.mutate(
        {
          form: submitData,
        },
        {
          onSuccess: () => {
            router.refresh();
            onSuccess?.();
            onOpenChange(false);
            toast.success("Nature sound created successfully");
            // Reset form
            setFormData({
              title: "",
              src: "",
              source: MediaSource.OTHER as MediaSource,
              categories: [],
              isPublic: true,
              isFeatured: false,
            });
          },
          onError: (error) => {
            console.error("Error creating nature sound:", error);
            toast.error("Failed to create nature sound");
          }
        }
      );
    }
  };

  // Check if loading or submitting
  const isLoading =
    (isEditMode && natureSoundQuery.isLoading) ||
    createNatureSoundMutation.isPending ||
    updateNatureSoundMutation.isPending;

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="sm:max-w-md md:max-w-lg lg:max-w-xl w-full p-0 focus:outline-none">
        <div className="h-full flex flex-col">
          <SheetHeader className="px-6 pt-6 pb-2">
            <SheetTitle className="text-xl font-semibold">{title}</SheetTitle>
            <SheetDescription>
              {isEditMode
                ? "Update your nature sound details below"
                : "Fill in the details to create new nature sound"
              }
            </SheetDescription>
          </SheetHeader>

          <div className="flex-1 overflow-y-auto px-6">
            <form id="nature-sound-form" onSubmit={handleSubmit} className="space-y-5 py-4">
              <div className="space-y-2">
                <Label htmlFor="title" className={errors.title ? 'text-destructive font-medium' : 'font-medium'}>
                  Title <span className="text-destructive">*</span>
                </Label>
                <Input
                  id="title"
                  name="title"
                  placeholder="Enter nature sound title"
                  value={formData.title}
                  onChange={handleChange}
                  className={errors.title ? 'border-destructive' : ''}
                  aria-required="true"
                  autoComplete="off"
                />
                {errors.title && (
                  <p className="text-sm text-destructive mt-1">{errors.title}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="src" className={errors.src ? 'text-destructive font-medium' : 'font-medium'}>
                  Audio URL
                </Label>
                <Input
                  id="src"
                  name="src"
                  placeholder="https://example.com/audio.mp3"
                  value={formData.src}
                  onChange={handleChange}
                  className={errors.src ? 'border-destructive' : ''}
                  autoComplete="off"
                />
                {errors.src && (
                  <p className="text-sm text-destructive mt-1">{errors.src}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="source" className="font-medium">
                  Source
                </Label>
                <Select value={formData.source} onValueChange={handleSourceChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a source" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(MediaSource).map((source) => (
                      <SelectItem key={source} value={source}>
                        {formatSourceName(source)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-3">
                <Label className={errors.categories ? 'text-destructive font-medium' : 'font-medium'}>
                  Categories <span className="text-destructive">*</span>
                </Label>
                <div className="grid grid-cols-2 gap-3">
                  {Object.values(NatureSoundCategory).map((category) => (
                    <div key={category} className="flex items-center space-x-2">
                      <Checkbox
                        id={`category-${category}`}
                        checked={formData.categories.includes(category)}
                        onCheckedChange={(checked) => handleCategoryChange(category, checked === true)}
                      />
                      <Label htmlFor={`category-${category}`} className="cursor-pointer">
                        {category}
                      </Label>
                    </div>
                  ))}
                </div>
                {errors.categories && (
                  <p className="text-sm text-destructive mt-1">{errors.categories}</p>
                )}
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="isPublic" className="font-medium">
                  Public Nature Sound
                </Label>
                <Switch
                  id="isPublic"
                  checked={formData.isPublic}
                  onCheckedChange={(checked) => handleSwitchChange('isPublic', checked === true)}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="isFeatured" className="font-medium">
                  Featured Nature Sound
                </Label>
                <Switch
                  id="isFeatured"
                  checked={formData.isFeatured}
                  onCheckedChange={(checked) => handleSwitchChange('isFeatured', checked === true)}
                />
              </div>
            </form>
          </div>

          <SheetFooter className="px-6 py-4 border-t">
            <Button
              type="submit"
              form="nature-sound-form"
              disabled={isLoading}
              className="w-full sm:w-auto"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {isEditMode ? 'Updating...' : 'Creating...'}
                </>
              ) : (
                <>
                  {isEditMode ? 'Update Nature Sound' : 'Create Nature Sound'}
                </>
              )}
            </Button>
          </SheetFooter>
        </div>
      </SheetContent>
    </Sheet>
  );
}