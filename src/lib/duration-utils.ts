/**
 * Utility functions for handling music duration formatting and conversion
 */

/**
 * Converts seconds to MM:SS format
 * @param seconds - Duration in seconds
 * @returns Formatted string in MM:SS format
 */
export function formatDuration(seconds: number | null | undefined): string {
  if (!seconds || seconds <= 0) {
    return "0:00";
  }
  
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins}:${secs.toString().padStart(2, '0')}`;
}

/**
 * Converts MM:SS format to total seconds
 * @param duration - Duration string in MM:SS format
 * @returns Total seconds as number
 */
export function parseDuration(duration: string): number {
  if (!duration || duration.trim() === "") {
    return 0;
  }
  
  const parts = duration.split(':');
  if (parts.length !== 2) {
    return 0;
  }
  
  const minutes = parseInt(parts[0]) || 0;
  const seconds = parseInt(parts[1]) || 0;
  
  // Validate seconds are between 0-59
  if (seconds < 0 || seconds > 59) {
    return 0;
  }
  
  return (minutes * 60) + seconds;
}

/**
 * Validates MM:SS format string
 * @param duration - Duration string to validate
 * @returns Object with isValid boolean and error message if invalid
 */
export function validateDurationFormat(duration: string): { isValid: boolean; error?: string } {
  if (!duration || duration.trim() === "") {
    return { isValid: true }; // Empty is valid (optional field)
  }
  
  const trimmed = duration.trim();
  const parts = trimmed.split(':');
  
  if (parts.length !== 2) {
    return { isValid: false, error: "Duration must be in MM:SS format" };
  }
  
  const minutes = parseInt(parts[0]);
  const seconds = parseInt(parts[1]);
  
  if (isNaN(minutes) || isNaN(seconds)) {
    return { isValid: false, error: "Duration must contain valid numbers" };
  }
  
  if (minutes < 0) {
    return { isValid: false, error: "Minutes cannot be negative" };
  }
  
  if (seconds < 0 || seconds > 59) {
    return { isValid: false, error: "Seconds must be between 0 and 59" };
  }
  
  return { isValid: true };
}

/**
 * Converts total seconds to separate minutes and seconds
 * @param totalSeconds - Total duration in seconds
 * @returns Object with minutes and seconds
 */
export function secondsToMinutesAndSeconds(totalSeconds: number | null | undefined): { minutes: number; seconds: number } {
  if (!totalSeconds || totalSeconds <= 0) {
    return { minutes: 0, seconds: 0 };
  }
  
  const minutes = Math.floor(totalSeconds / 60);
  const seconds = totalSeconds % 60;
  
  return { minutes, seconds };
}
