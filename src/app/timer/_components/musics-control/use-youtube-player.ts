'use client';

import { useState, useCallback, useRef, useEffect } from 'react';
import { YouTubePlayerState } from './types';
import { useGlobalMusicControl } from '@/lib/use-global-music-control';

declare global {
  interface Window {
    YT: any;
    onYouTubeIframeAPIReady: () => void;
  }
}

interface UseYouTubePlayerProps {
  onPlayStart?: () => void;
}

export function useYouTubePlayer({ onPlayStart }: UseYouTubePlayerProps = {}) {
  const [playerState, setPlayerState] = useState<YouTubePlayerState>({
    currentVideoId: '', // No default video ID to prevent auto-creation
    isPlaying: false,
    volume: 70,
    isMuted: false,
    inputUrl: 'https://www.youtube.com/watch?v=hlWiI4xVXKY'
  });

  const playerRef = useRef<any>(null);
  const isAPILoadedRef = useRef(false);
  const isPlayerReadyRef = useRef(false);

  // Tab visibility handling
  const wasPlayingBeforeHiddenRef = useRef(false);
  const visibilityRetryTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const visibilityRetryCountRef = useRef(0);

  // Global music control integration
  const globalMusicControl = useGlobalMusicControl({
    sourceId: 'youtube-player',
    sourceType: 'youtube',
    sourceName: 'YouTube Player',
    persistOnUnmount: true, // Prevent audio from stopping when UI is hidden
    onPlayStart: () => {
      if (onPlayStart) {
        onPlayStart();
      }
    },
    onPause: () => {
      // Pause the YouTube player when requested by global control
      if (playerRef.current && isPlayerReadyRef.current && typeof playerRef.current.pauseVideo === 'function') {
        try {
          playerRef.current.pauseVideo();
        } catch (error) {
          console.error('Error pausing YouTube video from global control:', error);
        }
      }
    },
  });

  // Extract video ID from YouTube URL
  const extractVideoId = useCallback((url: string): string | null => {
    const patterns = [
      /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,
      /^([a-zA-Z0-9_-]{11})$/ // Direct video ID
    ];

    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match) return match[1];
    }
    return null;
  }, []);

  const createPlayer = useCallback(() => {
    if (!window.YT || !window.YT.Player || !playerState.currentVideoId) return;

    try {
      // Destroy existing player if it exists
      if (playerRef.current && typeof playerRef.current.destroy === 'function') {
        playerRef.current.destroy();
      }

      playerRef.current = new window.YT.Player('youtube-player', {
        height: '100%',
        width: '100%',
        videoId: playerState.currentVideoId,
        playerVars: {
          autoplay: 0,
          controls: 1,
          modestbranding: 1,
          rel: 0,
          showinfo: 0,
          iv_load_policy: 3,
          disablekb: 0,
          fs: 1,
          cc_load_policy: 0,
          origin: window.location.origin,
          enablejsapi: 1,
          // Optimizations for background playback
          playsinline: 1,
          widget_referrer: window.location.origin,
        },
        events: {
          onReady: (event: any) => {
            isPlayerReadyRef.current = true;
            event.target.setVolume(playerState.volume);
            if (playerState.isMuted) {
              event.target.mute();
            }
          },
          onStateChange: (event: any) => {
            const isCurrentlyPlaying = event.data === window.YT.PlayerState.PLAYING;
            const wasPlaying = playerState.isPlaying;

            setPlayerState(prev => ({ ...prev, isPlaying: isCurrentlyPlaying }));

            // Update global music control state
            if (isCurrentlyPlaying && !wasPlaying) {
              // Notify global control that YouTube is starting to play
              const videoTitle = playerRef.current?.getVideoData?.()?.title || 'YouTube Video';
              globalMusicControl.notifyPlayStart({
                id: playerState.currentVideoId,
                title: videoTitle,
                src: `https://www.youtube.com/watch?v=${playerState.currentVideoId}`,
                type: 'music',
              });
            } else if (!isCurrentlyPlaying && wasPlaying) {
              // Notify global control that YouTube has paused
              globalMusicControl.notifyPause();
            }

            // Update global state with current playback info
            globalMusicControl.updateState({
              isPlaying: isCurrentlyPlaying,
              volume: playerState.volume,
              isMuted: playerState.isMuted,
            });
          },
          onError: (event: any) => {
            console.error('YouTube player error:', event.data);
            isPlayerReadyRef.current = false;
          }
        },
      });
    } catch (error) {
      console.error('Error creating YouTube player:', error);
      isPlayerReadyRef.current = false;
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [playerState.currentVideoId, playerState.volume, playerState.isMuted, playerState.isPlaying]); // globalMusicControl is stable

  // Handle tab visibility changes to maintain background playback
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!playerRef.current || !isPlayerReadyRef.current) return;

      if (document.hidden) {
        // Tab became hidden - store current playing state
        try {
          const currentState = playerRef.current.getPlayerState();
          wasPlayingBeforeHiddenRef.current = currentState === window.YT?.PlayerState?.PLAYING;
          console.log('Desktop YouTube Player: Tab hidden, was playing:', wasPlayingBeforeHiddenRef.current);
        } catch (error) {
          console.error('Desktop YouTube Player: Error checking player state on hide:', error);
        }
      } else {
        // Tab became visible - attempt to resume playback if it was playing before
        if (wasPlayingBeforeHiddenRef.current) {
          console.log('Desktop YouTube Player: Tab visible, attempting to resume playback');

          // Clear any existing retry timeout
          if (visibilityRetryTimeoutRef.current) {
            clearTimeout(visibilityRetryTimeoutRef.current);
          }

          // Reset retry count
          visibilityRetryCountRef.current = 0;

          // Attempt to resume playback with retries
          const attemptResume = () => {
            try {
              if (playerRef.current && isPlayerReadyRef.current) {
                const currentState = playerRef.current.getPlayerState();

                // Only try to resume if not already playing
                if (currentState !== window.YT?.PlayerState?.PLAYING) {
                  playerRef.current.playVideo();
                  console.log('Desktop YouTube Player: Resume attempt', visibilityRetryCountRef.current + 1);

                  // Verify playback started after a short delay
                  setTimeout(() => {
                    if (playerRef.current && isPlayerReadyRef.current) {
                      const newState = playerRef.current.getPlayerState();
                      if (newState !== window.YT?.PlayerState?.PLAYING && visibilityRetryCountRef.current < 3) {
                        visibilityRetryCountRef.current++;
                        visibilityRetryTimeoutRef.current = setTimeout(attemptResume, 1000);
                      } else if (newState === window.YT?.PlayerState?.PLAYING) {
                        console.log('Desktop YouTube Player: Successfully resumed playback');
                        wasPlayingBeforeHiddenRef.current = false; // Reset flag
                      }
                    }
                  }, 500);
                } else {
                  console.log('Desktop YouTube Player: Already playing, no resume needed');
                  wasPlayingBeforeHiddenRef.current = false; // Reset flag
                }
              }
            } catch (error) {
              console.error('Desktop YouTube Player: Error attempting to resume playback:', error);
            }
          };

          // Start resume attempt after a short delay to let the tab fully activate
          visibilityRetryTimeoutRef.current = setTimeout(attemptResume, 100);
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      if (visibilityRetryTimeoutRef.current) {
        clearTimeout(visibilityRetryTimeoutRef.current);
      }
    };
  }, []);

  // Load YouTube IFrame API but don't create player yet
  useEffect(() => {
    if (isAPILoadedRef.current) return;

    const loadYouTubeAPI = () => {
      const tag = document.createElement('script');
      tag.src = 'https://www.youtube.com/iframe_api';
      const firstScriptTag = document.getElementsByTagName('script')[0];
      firstScriptTag.parentNode?.insertBefore(tag, firstScriptTag);
    };

    if (!window.YT) {
      loadYouTubeAPI();
    }

    window.onYouTubeIframeAPIReady = () => {
      isAPILoadedRef.current = true;
      // Don't auto-create player here - wait for user to load a video
    };

    if (window.YT && window.YT.Player) {
      isAPILoadedRef.current = true;
      // Don't auto-create player here - wait for user to load a video
    }

    return () => {
      if (playerRef.current && typeof playerRef.current.destroy === 'function') {
        try {
          playerRef.current.destroy();
        } catch (error) {
          console.error('Error destroying YouTube player:', error);
        }
      }
      isPlayerReadyRef.current = false;

      // Clean up visibility handling
      if (visibilityRetryTimeoutRef.current) {
        clearTimeout(visibilityRetryTimeoutRef.current);
      }
    };
  }, []); // Remove createPlayer dependency

  const togglePlay = useCallback(() => {
    if (!playerRef.current || !isPlayerReadyRef.current) return;

    try {
      if (playerState.isPlaying) {
        playerRef.current.pauseVideo();
        globalMusicControl.notifyPause();
      } else {
        // Request control from global music system before playing
        const canPlay = globalMusicControl.requestControl();
        if (canPlay) {
          playerRef.current.playVideo();
          // The onStateChange handler will call notifyPlayStart when playback actually starts
        }
      }
    } catch (error) {
      console.error('Error toggling YouTube player:', error);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [playerState.isPlaying]); // globalMusicControl is stable

  const handleVolumeChange = useCallback((volume: number) => {
    setPlayerState(prev => ({ ...prev, volume, isMuted: false }));
    if (playerRef.current && isPlayerReadyRef.current) {
      try {
        playerRef.current.setVolume(volume);
        if (volume > 0) {
          playerRef.current.unMute();
        }
      } catch (error) {
        console.error('Error changing YouTube player volume:', error);
      }
    }

    // Update global state
    globalMusicControl.updateState({
      volume,
      isMuted: false,
    });
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // globalMusicControl is stable

  const toggleMute = useCallback(() => {
    const newMutedState = !playerState.isMuted;
    setPlayerState(prev => ({ ...prev, isMuted: newMutedState }));

    if (playerRef.current && isPlayerReadyRef.current) {
      try {
        if (playerState.isMuted) {
          playerRef.current.unMute();
        } else {
          playerRef.current.mute();
        }
      } catch (error) {
        console.error('Error toggling YouTube player mute:', error);
      }
    }

    // Update global state
    globalMusicControl.updateState({
      isMuted: newMutedState,
    });
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [playerState.isMuted]); // globalMusicControl is stable

  const handleUrlChange = useCallback((url: string) => {
    setPlayerState(prev => ({ ...prev, inputUrl: url }));
  }, []);

  const loadVideo = useCallback(() => {
    const videoId = extractVideoId(playerState.inputUrl);
    if (!videoId) {
      alert('Please enter a valid YouTube URL');
      return;
    }

    setPlayerState(prev => ({ ...prev, currentVideoId: videoId }));
    
    // Check if player exists and is ready
    if (playerRef.current && isPlayerReadyRef.current && typeof playerRef.current.loadVideoById === 'function') {
      try {
        // Stop current video first to prevent audio overlap
        if (typeof playerRef.current.stopVideo === 'function') {
          playerRef.current.stopVideo();
        }
        
        // Load the new video without recreating the player
        playerRef.current.loadVideoById({
          videoId: videoId,
          startSeconds: 0
        });
        
        // Set volume and mute state for new video
        setTimeout(() => {
          if (playerRef.current && isPlayerReadyRef.current) {
            playerRef.current.setVolume(playerState.volume);
            if (playerState.isMuted) {
              playerRef.current.mute();
            }
            
            // Auto-play the new video if global control allows it
            if (typeof playerRef.current.playVideo === 'function') {
              const canPlay = globalMusicControl.requestControl();
              if (canPlay) {
                playerRef.current.playVideo();
                // The onStateChange handler will call notifyPlayStart when playback actually starts
              }
            }
          }
        }, 500);
      } catch (error) {
        console.error('Error loading YouTube video:', error);
        // Fallback: recreate player with new video only if loadVideoById fails
        createPlayer();
      }
    } else {
      // Player doesn't exist or not ready, create it
      if (isAPILoadedRef.current) {
        createPlayer();
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [playerState.inputUrl, playerState.volume, playerState.isMuted, extractVideoId, createPlayer]); // globalMusicControl is stable

  // Create player when currentVideoId changes (after loadVideo sets it)
  useEffect(() => {
    if (playerState.currentVideoId && isAPILoadedRef.current && !playerRef.current) {
      createPlayer();
    }
  }, [playerState.currentVideoId, createPlayer]);

  const pauseVideo = useCallback(() => {
    if (playerRef.current && isPlayerReadyRef.current && typeof playerRef.current.pauseVideo === 'function') {
      try {
        playerRef.current.pauseVideo();
        globalMusicControl.notifyPause();
      } catch (error) {
        console.error('Error pausing YouTube video:', error);
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // globalMusicControl is stable

  // Cleanup function for navigation
  const cleanupForNavigation = useCallback(() => {
    if (playerRef.current && isPlayerReadyRef.current) {
      try {
        playerRef.current.pauseVideo();
        globalMusicControl.notifyStop();
        console.log('YouTube Player: Cleaned up for navigation');
      } catch (error) {
        console.error('Error cleaning up YouTube player for navigation:', error);
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // globalMusicControl is stable

  return {
    playerState,
    togglePlay,
    handleVolumeChange,
    toggleMute,
    handleUrlChange,
    loadVideo,
    pauseVideo,
    cleanupForNavigation,
    playerRef,
    isPlayerReady: isPlayerReadyRef.current,
  };
} 