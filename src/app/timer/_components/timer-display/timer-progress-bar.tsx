'use client';

import { cn } from '@/lib/utils';
import { Progress } from '@/components/ui/progress';
import { TimerColorPreset } from '@/lib/pomodoro-store';
import { getProgressBarColor } from './common/constants';

interface TimerProgressBarProps {
  progressPercentage: number;
  isRunning: boolean;
  showControls: boolean;
  timerColor: TimerColorPreset;
  timerSize?: {
    timeScale?: number;
  } | null;
}

export function TimerProgressBar({
  progressPercentage,
  isRunning,
  showControls,
  timerColor,
  timerSize
}: TimerProgressBarProps) {
  return (
    <div className={cn(
      "w-full transition-properties ease-out relative flex justify-center",
      showControls ? "mt-1" : isRunning ? "mt-0.5" : "mt-0.5"
    )}
    style={{
      marginTop: timerSize?.timeScale && timerSize.timeScale > 1.3
        ? `${Math.max(0.5, Math.min(1.2, 0.5 + (timerSize.timeScale - 1.3) * 0.7))}rem`
        : undefined,
      minHeight: timerSize?.timeScale && timerSize.timeScale > 1.3
        ? showControls
          ? `${Math.max(12, Math.min(16, 12 + (timerSize.timeScale - 1.3) * 4))}px`
          : `${Math.max(8, Math.min(12, 8 + (timerSize.timeScale - 1.3) * 3))}px`
        : showControls ? '10px' : '6px',
      paddingTop: timerSize?.timeScale && timerSize.timeScale > 1.3
        ? `${Math.max(0.2, Math.min(0.5, 0.2 + (timerSize.timeScale - 1.3) * 0.3))}rem`
        : undefined,
      paddingBottom: timerSize?.timeScale && timerSize.timeScale > 1.3
        ? `${Math.max(0.1, Math.min(0.3, 0.1 + (timerSize.timeScale - 1.3) * 0.2))}rem`
        : undefined
    }}
    >
      {/* Progress percentage indicator - enhanced for glass backgrounds */}
      {showControls && (
        <div
          className="absolute -top-4 right-0 text-[10px] text-white/90 font-medium timer-control-fade show"
          style={{
            animationDelay: '75ms',
            textShadow: '0 1px 3px rgba(0, 0, 0, 0.5), 0 1px 2px rgba(0, 0, 0, 0.3)' // Enhanced shadow for glass readability
          }}
        >
          {Math.round(progressPercentage)}%
        </div>
      )}

      <Progress
        value={progressPercentage}
        className={cn(
          "transition-properties ease-out rounded-full overflow-hidden w-[85%]",
          "bg-white/10"
        )}
        style={{
          height: timerSize?.timeScale && timerSize.timeScale > 1.3
            ? showControls
              ? `${Math.max(8, Math.min(12, 8 + (timerSize.timeScale - 1.3) * 4))}px`
              : `${Math.max(6, Math.min(10, 6 + (timerSize.timeScale - 1.3) * 3))}px`
            : showControls ? '8px' : '6px',
          opacity: showControls
            ? 1
            : isRunning
              ? 0.8
              : 0.9
        }}
        indicatorClassName={cn(
          getProgressBarColor(timerColor),
          isRunning && !showControls ? "opacity-90 progress-pulse" : "",
          "transition-properties ease-out"
        )} 
        aria-label={`${progressPercentage}% completed`}
      />
    </div>
  );
} 