import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON>ei<PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/components/providers/theme-provider";
import { Toaster } from "@/components/ui/sonner";
import { BetterAuthProviders } from "@/components/providers/better-auth-providers";
import { QueryProvider } from "@/components/providers/query-provider";
// import { ServiceWorkerRegister } from "@/components/pwa/service-worker-register";
import { cn } from "@/lib/utils";
import { CheckAuth } from "./auth/check-auth";
import { NavigationAudioCleanup } from "@/components/audio/navigation-audio-cleanup";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Pomodoro 365 - Focus Timer with Ambient Music & Beautiful Backgrounds",
  description: "Boost productivity with Pomodoro 365's customizable focus timer, soothing ambient music, and beautiful backgrounds. Track progress, set personalized work sessions, and enhance your focus in style.",
  keywords: ["pomodoro timer", "focus timer", "ambient music", "productivity tool", "beautiful backgrounds", "work sessions", "study timer", "concentration app", "time management", "relaxing music"],
  authors: [{ name: "Pomodoro 365 Team" }],
  openGraph: {
    title: "Pomodoro 365 - Focus Timer with Ambient Music & Beautiful Backgrounds",
    description: "Enhance focus with customizable timers, soothing ambient music, and beautiful backgrounds to create your perfect productivity environment.",
    images: ['pomodoro.jpg'],
    type: "website",
    locale: "en_US",
    url: "https://pomodoro365.com",
  },
  twitter: {
    card: "summary_large_image",
    title: "Pomodoro 365 - Focus Timer with Ambient Music & Beautiful Backgrounds",
    description: "Enhance focus with customizable timers, soothing ambient music, and beautiful backgrounds.",
    images: ['pomodoro.jpg'],
  },
  icons: {
    icon: [
      { url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
      { url: '/favicon-48x48.png', sizes: '48x48', type: 'image/png' },
      { url: '/favicon.ico', sizes: 'any' },
    ],
    apple: [
      { url: '/apple-touch-icon.png', sizes: '180x180', type: 'image/png' },
    ],
    shortcut: '/favicon.ico',
  },
  metadataBase: new URL("https://pomodoro365.com"),
  alternates: {
    canonical: "/",
  },
  manifest: "/site.webmanifest",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={cn(geistSans.variable, geistMono.variable, "h-full")} suppressHydrationWarning>
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem
          disableTransitionOnChange
        >
          <QueryProvider>
            <BetterAuthProviders>
              {/* <ServiceWorkerRegister /> */}
              <Toaster position="bottom-center" />
              <CheckAuth />
              <NavigationAudioCleanup />
              {children}
            </BetterAuthProviders>
          </QueryProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
