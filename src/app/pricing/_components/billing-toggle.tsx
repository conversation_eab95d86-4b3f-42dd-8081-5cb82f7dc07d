'use client';

import { motion } from 'framer-motion';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface BillingToggleProps {
  isAnnual: boolean;
  onToggle: (isAnnual: boolean) => void;
}

export const BillingToggle = ({ isAnnual, onToggle }: BillingToggleProps) => {
  return (
    <div className="flex items-center justify-center mb-10">
      <div className="flex items-center gap-6 p-1 bg-muted/30 rounded-full">
        <span className={cn(
          "text-sm font-medium transition-colors duration-200 px-3 py-2",
          !isAnnual ? "text-foreground" : "text-muted-foreground"
        )}>
          Monthly
        </span>

        <Switch
          checked={isAnnual}
          onCheckedChange={onToggle}
          className={cn(
            "data-[state=checked]:bg-gradient-to-r data-[state=checked]:from-orange-500 data-[state=checked]:to-red-500",
            "data-[state=unchecked]:bg-muted-foreground/20"
          )}
        />

        <div className="flex items-center gap-3">
          <span className={cn(
            "text-sm font-medium transition-colors duration-200 px-3 py-2",
            isAnnual ? "text-foreground" : "text-muted-foreground"
          )}>
            Annual
          </span>
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{
              opacity: isAnnual ? 1 : 0,
              scale: isAnnual ? 1 : 0.8
            }}
            transition={{
              duration: 0.2,
              ease: "easeOut"
            }}
            className="flex"
          >
            <Badge className="bg-gradient-to-r from-green-500 to-emerald-500 text-white text-xs font-medium px-2 py-1 shadow-sm">
              Save 47%
            </Badge>
          </motion.div>
        </div>
      </div>
    </div>
  );
};
