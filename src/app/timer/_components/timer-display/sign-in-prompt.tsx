'use client';

import { useCallback, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Spark<PERSON> } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { useUserStore } from '@/store/userStore';
import { cn } from '@/lib/utils';

interface SignInPromptProps {
  isVisible: boolean;
  onClose: () => void;
  onSignIn: () => void;
  className?: string;
}

export function SignInPrompt({
  isVisible,
  onClose,
  onSignIn,
  className
}: SignInPromptProps) {
  const { updatePreferences } = useUserStore();

  const handleDismiss = useCallback(() => {
    try {
      updatePreferences({
        ui: {
          dismissedSignInPrompt: true,
        },
      });
    } catch (error) {
      console.error('Failed to update sign-in prompt dismissal preference:', error);
    }
    onClose();
  }, [updatePreferences, onClose]);

  const handleSignIn = useCallback(() => {
    onSignIn();
  }, [onSignIn]);

  // Auto-hide after 8 seconds
  useEffect(() => {
    if (!isVisible) return;

    const autoHideTimer = setTimeout(() => {
      handleDismiss();
    }, 8000);

    return () => clearTimeout(autoHideTimer);
  }, [isVisible, handleDismiss]);

  return (
    <Dialog open={isVisible} onOpenChange={(open) => !open && handleDismiss()}>
      <DialogContent className={cn("sm:max-w-sm p-4", className)}>
        <DialogHeader className="text-center space-y-2">
          <div className="mx-auto flex h-10 w-10 items-center justify-center rounded-full bg-primary/10">
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.2 }}
            >
              <Sparkles className="h-5 w-5 text-primary" />
            </motion.div>
          </div>
          <DialogTitle className="text-lg font-semibold">
            Great session!
          </DialogTitle>
          <DialogDescription className="text-sm text-muted-foreground">
            Save progress & unlock features
          </DialogDescription>
        </DialogHeader>

        <motion.div
          className="space-y-3 pt-2"
          initial={{ opacity: 0, y: 5 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1, duration: 0.2 }}
        >
          {/* Simplified benefits */}
          <div className="text-xs text-muted-foreground text-center space-y-1">
            <p>• Track streaks • Save history • Sync devices</p>
          </div>

          {/* Actions */}
          <div className="flex flex-col gap-2">
            <Button
              onClick={handleSignIn}
              className="w-full h-9"
              size="sm"
            >
              Sign In
            </Button>
            <Button
              onClick={handleDismiss}
              variant="ghost"
              size="sm"
              className="w-full h-8 text-xs text-muted-foreground hover:text-foreground"
            >
              Later
            </Button>
          </div>

          {/* Compact progress indicator */}
          <div className="relative">
            <div className="h-0.5 w-full bg-muted rounded-full overflow-hidden">
              <motion.div
                initial={{ width: "100%" }}
                animate={{ width: "0%" }}
                transition={{ duration: 8, ease: "linear" }}
                className="h-full bg-primary/50 rounded-full"
              />
            </div>
          </div>
        </motion.div>
      </DialogContent>
    </Dialog>
  );
}
