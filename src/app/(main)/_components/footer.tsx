'use client';

import { Button } from '@/components/ui/button';
import Link from 'next/link';

export const Footer = () => {
  return (
    <footer className="w-full border-t border-slate-200 dark:border-slate-800 bg-white/50 dark:bg-slate-900/50 backdrop-blur-sm py-8 mt-auto">
      <div className="container mx-auto px-4 max-w-7xl">
        <div className="flex flex-col md:flex-row justify-between items-center gap-4">
          <p className="text-sm text-slate-500 dark:text-slate-400">
            Pomodoro 365 &copy; {new Date().getFullYear()} - Boost your productivity with focus
          </p>
          <div className="flex gap-6">
            <Link href="/pricing">
              <Button variant="ghost" size="sm" className="hover:bg-slate-100 dark:hover:bg-slate-800 text-slate-600 dark:text-slate-300">
                Pricing
              </Button>
            </Link>
            <Button variant="ghost" size="sm" className="hover:bg-slate-100 dark:hover:bg-slate-800 text-slate-600 dark:text-slate-300">About</Button>
            <Button variant="ghost" size="sm" className="hover:bg-slate-100 dark:hover:bg-slate-800 text-slate-600 dark:text-slate-300">Privacy</Button>
            <Button variant="ghost" size="sm" className="hover:bg-slate-100 dark:hover:bg-slate-800 text-slate-600 dark:text-slate-300">Contact</Button>
          </div>
        </div>
      </div>
    </footer>
  );
}; 