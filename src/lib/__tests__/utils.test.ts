import { formatDurationReadable, formatMinutesReadable } from '../utils'

describe('Time formatting utilities', () => {
  describe('formatDurationReadable', () => {
    it('should format zero seconds as "0m"', () => {
      expect(formatDurationReadable(0)).toBe('0m')
      expect(formatDurationReadable(-5)).toBe('0m')
    })

    it('should format minutes only for less than 1 hour', () => {
      expect(formatDurationReadable(60)).toBe('1m')
      expect(formatDurationReadable(1800)).toBe('30m')
      expect(formatDurationReadable(3540)).toBe('59m')
    })

    it('should format hours only for exact hours', () => {
      expect(formatDurationReadable(3600)).toBe('1h')
      expect(formatDurationReadable(7200)).toBe('2h')
      expect(formatDurationReadable(36000)).toBe('10h')
    })

    it('should format hours and minutes for mixed durations', () => {
      expect(formatDurationReadable(3660)).toBe('1h 1m')
      expect(formatDurationReadable(4500)).toBe('1h 15m')
      expect(formatDurationReadable(13500)).toBe('3h 45m')
    })
  })

  describe('formatMinutesReadable', () => {
    it('should format zero minutes as "0m"', () => {
      expect(formatMinutesReadable(0)).toBe('0m')
      expect(formatMinutesReadable(-5)).toBe('0m')
    })

    it('should format minutes only for less than 60 minutes', () => {
      expect(formatMinutesReadable(1)).toBe('1m')
      expect(formatMinutesReadable(30)).toBe('30m')
      expect(formatMinutesReadable(59)).toBe('59m')
    })

    it('should format hours only for exact hours', () => {
      expect(formatMinutesReadable(60)).toBe('1h')
      expect(formatMinutesReadable(120)).toBe('2h')
      expect(formatMinutesReadable(600)).toBe('10h')
    })

    it('should format hours and minutes for mixed durations', () => {
      expect(formatMinutesReadable(61)).toBe('1h 1m')
      expect(formatMinutesReadable(75)).toBe('1h 15m')
      expect(formatMinutesReadable(225)).toBe('3h 45m')
    })
  })
})
