import { z } from 'zod';
import { UserRoleSchema } from '../inputTypeSchemas/UserRoleSchema'
import { MusicGenreSchema } from '../inputTypeSchemas/MusicGenreSchema'
import { UserWithRelationsSchema, UserPartialWithRelationsSchema } from './UserSchema'
import type { UserWithRelations, UserPartialWithRelations } from './UserSchema'
import { MusicWithRelationsSchema, MusicPartialWithRelationsSchema } from './MusicSchema'
import type { MusicWithRelations, MusicPartialWithRelations } from './MusicSchema'
import { VideoWithRelationsSchema, VideoPartialWithRelationsSchema } from './VideoSchema'
import type { VideoWithRelations, VideoPartialWithRelations } from './VideoSchema'

/////////////////////////////////////////
// MUSIC PLAYLIST SCHEMA
/////////////////////////////////////////

export const MusicPlaylistSchema = z.object({
  creatorType: UserRoleSchema,
  genres: MusicGenreSchema.array(),
  id: z.string().cuid(),
  name: z.string().min(1),
  description: z.string().nullish(),
  imageUrl: z.union([z.instanceof(File), z.string().nullable()]).nullish(),
  isPublic: z.boolean(),
  isDefault: z.boolean(),
  userId: z.string(),
  musicOrder: z.string().array(),
  createdAt: z.union([z.date(), z.string().datetime()]),
  updatedAt: z.union([z.date(), z.string().datetime()]),
})

export type MusicPlaylist = z.infer<typeof MusicPlaylistSchema>

/////////////////////////////////////////
// MUSIC PLAYLIST PARTIAL SCHEMA
/////////////////////////////////////////

export const MusicPlaylistPartialSchema = MusicPlaylistSchema.partial()

export type MusicPlaylistPartial = z.infer<typeof MusicPlaylistPartialSchema>

/////////////////////////////////////////
// MUSIC PLAYLIST RELATION SCHEMA
/////////////////////////////////////////

export type MusicPlaylistRelations = {
  user: UserWithRelations;
  musics: MusicWithRelations[];
  videos: VideoWithRelations[];
};

export type MusicPlaylistWithRelations = z.infer<typeof MusicPlaylistSchema> & MusicPlaylistRelations

export const MusicPlaylistWithRelationsSchema: z.ZodType<MusicPlaylistWithRelations> = MusicPlaylistSchema.merge(z.object({
  user: z.lazy(() => UserWithRelationsSchema),
  musics: z.lazy(() => MusicWithRelationsSchema).array(),
  videos: z.lazy(() => VideoWithRelationsSchema).array(),
}))

/////////////////////////////////////////
// MUSIC PLAYLIST PARTIAL RELATION SCHEMA
/////////////////////////////////////////

export type MusicPlaylistPartialRelations = {
  user?: UserPartialWithRelations;
  musics?: MusicPartialWithRelations[];
  videos?: VideoPartialWithRelations[];
};

export type MusicPlaylistPartialWithRelations = z.infer<typeof MusicPlaylistPartialSchema> & MusicPlaylistPartialRelations

export const MusicPlaylistPartialWithRelationsSchema: z.ZodType<MusicPlaylistPartialWithRelations> = MusicPlaylistPartialSchema.merge(z.object({
  user: z.lazy(() => UserPartialWithRelationsSchema),
  musics: z.lazy(() => MusicPartialWithRelationsSchema).array(),
  videos: z.lazy(() => VideoPartialWithRelationsSchema).array(),
})).partial()

export type MusicPlaylistWithPartialRelations = z.infer<typeof MusicPlaylistSchema> & MusicPlaylistPartialRelations

export const MusicPlaylistWithPartialRelationsSchema: z.ZodType<MusicPlaylistWithPartialRelations> = MusicPlaylistSchema.merge(z.object({
  user: z.lazy(() => UserPartialWithRelationsSchema),
  musics: z.lazy(() => MusicPartialWithRelationsSchema).array(),
  videos: z.lazy(() => VideoPartialWithRelationsSchema).array(),
}).partial())

export default MusicPlaylistSchema;
