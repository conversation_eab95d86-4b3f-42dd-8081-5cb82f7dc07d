import { Volume1, Volume2, VolumeX } from "lucide-react";

// Get volume icon based on current volume
export function getVolumeIcon(volume: number, isMuted: boolean) {
  if (isMuted) return VolumeX;
  if (volume === 0) return VolumeX;
  if (volume < 50) return Volume1;
  return Volume2;
}

// Get volume icon for nature sound (flexible type)
export function getNatureSoundVolumeIcon(sound: { volume: number; isMuted: boolean }) {
  if (sound.isMuted) return VolumeX;
  if (sound.volume === 0) return VolumeX;
  if (sound.volume < 50) return Volume1;
  return Volume2;
}