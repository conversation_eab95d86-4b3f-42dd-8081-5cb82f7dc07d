"use client"

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { motion } from "framer-motion"
import { FocusTimeChart } from "@/components/stats/FocusTimeChart"
import { CompletionRateChart } from "@/components/stats/CompletionRateChart"
import { WeeklyComparison } from "@/components/stats/WeeklyComparison"
import { FocusDistribution } from "@/components/stats/FocusDistribution"
import { DailyHeatmap } from "@/components/stats/DailyHeatmap"
import { StreakCalendar } from "@/components/stats/StreakCalendar"
import { HorizontalSessionTimeline } from "@/components/stats/HorizontalSessionTimeline"
import { TrendingUp, Bar<PERSON><PERSON>, PieChart, Clock, Calendar, Target, Activity, Zap, Flame, Timer } from "lucide-react"
import { formatMinutesReadable, formatDurationReadable } from "@/lib/utils"

interface TodaySession {
  title?: string
  timeRange: string
  duration: number
  completed: boolean
}

interface Session {
  id: string
  title: string
  type: "focus" | "shortBreak" | "longBreak"
  startTime: string
  endTime: string
  completed: boolean
}

interface AnalyticsOverviewTabProps {
  transformedData: {
    dailyFocusTime: Array<{ date: string; minutes: number }>
    completionRate: { completed: number; interrupted: number }
    weeklyComparison: Array<{ name: string; thisWeek: number; lastWeek: number }>
    hourlyDistribution: Array<{ hour: string; value: number }>
    dailyHeatmap: Array<{ day: string; hour: number; value: number }>
    todaySessions: TodaySession[]
    todayQuality: number
    recommendation: string
    streakData: Array<{ date: string; count: number }>
    daysActiveThisMonth: number
    longestStreak: number
    consistencyScore: number
    dailyCalendarSessions: Session[]
  }
  safeStatsData: {
    focusSessions: number
    shortBreakSessions: number
    longBreakSessions: number
    totalSessions: number
    completedSessions: number
    focusDuration: number
    shortBreakDuration: number
    longBreakDuration: number
  }
  timelineSessionData?: Session[]
  selectedTimelineDate?: string
  onTimelineDateChange?: (date: string) => void
}

export function AnalyticsOverviewTab({
  transformedData,
  safeStatsData,
  timelineSessionData,
  selectedTimelineDate,
  onTimelineDateChange
}: AnalyticsOverviewTabProps) {
  function formatTime(timeStr: string) {
    // Handles both 'HH:mm:ss' and ISO strings
    const date = timeStr.length > 8 ? new Date(timeStr) : new Date(`1970-01-01T${timeStr}`);
    return date.toLocaleTimeString([], { hour: 'numeric', minute: '2-digit' });
  }

  function formatTimeRange(timeRange: string) {
    // Expects 'start - end' (e.g., '14:00:00 - 14:25:00' or ISO)
    const [start, end] = timeRange.split(' - ');
    return `${formatTime(start)} - ${formatTime(end)}`;
  }

  // Animation variants that respect user preferences
  const getAnimationProps = (delay: number = 0) => ({
    initial: { opacity: 0, x: -20 },
    animate: { opacity: 1, x: 0 },
    transition: {
      duration: 0.3,
      delay,
      ease: "easeInOut"
    }
  });

  const getVerticalAnimationProps = (delay: number = 0) => ({
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: {
      duration: 0.3,
      delay,
      ease: "easeInOut"
    }
  });

  return (
    <div className="space-y-6">
      {/* Core Metrics Section - Row 1: Focus Time Trend & Weekly Comparison */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card className="bg-gradient-to-br from-chart-3/[0.02] to-chart-3/[0.05] bg-card border-border shadow-lg hover:shadow-xl transition-all duration-300">
          <CardHeader className="pb-4">
            <CardTitle className="text-lg font-semibold flex items-center text-foreground">
              <div className="mr-3 p-2 rounded-lg bg-orange-500/10 border border-orange-500/20 hover:bg-orange-500/15 transition-colors duration-200">
                <TrendingUp className="h-4 w-4 text-orange-500" aria-hidden="true" />
              </div>
              <span>Focus Time Trend</span>
            </CardTitle>
            <CardDescription className="text-sm text-muted-foreground">
              Track your daily focus patterns and productivity trends over the past 30 days
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-0" role="region" aria-label="Focus time trend analysis">
            <FocusTimeChart data={transformedData.dailyFocusTime} />
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-chart-2/[0.02] to-chart-2/[0.05] bg-card border-border shadow-lg hover:shadow-xl transition-all duration-300">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg font-semibold flex items-center text-foreground">
              <div className="mr-3 p-2 rounded-lg bg-chart-2/10 border border-chart-2/20 hover:bg-chart-2/15 transition-colors duration-200">
                <BarChart className="h-4 w-4 text-chart-2" aria-hidden="true" />
              </div>
              <span>Weekly Comparison</span>
            </CardTitle>
            <CardDescription className="text-sm text-muted-foreground">
              Compare your focus time distribution across weeks
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-0" role="region" aria-label="Weekly focus time comparison across days">
            <WeeklyComparison data={transformedData.weeklyComparison} />
          </CardContent>
        </Card>
      </div>

      {/* Core Metrics Section - Row 2: Completion Rate & Focus Distribution */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card className="bg-gradient-to-br from-chart-3/[0.02] to-chart-3/[0.05] bg-card border-border shadow-lg">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg font-semibold flex items-center text-foreground">
              <div className="mr-3 p-2 rounded-lg bg-chart-3/10 border border-border">
                <PieChart className="h-4 w-4 text-chart-3" />
              </div>
              Completion Rate
            </CardTitle>
            <CardDescription className="text-sm text-muted-foreground">Completed vs. interrupted sessions over the last 30 days (a session is <strong>completed</strong> when the focus timer finishes without manual interruption)</CardDescription>
          </CardHeader>
          <CardContent className="pt-0">
            <CompletionRateChart data={transformedData.completionRate} periodLabel="Last 30 days" />
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-chart-4/[0.02] to-chart-4/[0.05] bg-card border-border shadow-lg hover:shadow-xl transition-all duration-300">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg font-semibold flex items-center text-foreground">
              <div className="mr-3 p-2 rounded-lg bg-chart-4/10 border border-chart-4/20 hover:bg-chart-4/15 transition-colors duration-200">
                <Clock className="h-4 w-4 text-chart-4" aria-hidden="true" />
              </div>
              <span>Focus Distribution</span>
            </CardTitle>
            <CardDescription className="text-sm text-muted-foreground">
              When you focus most during the day
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-0" role="region" aria-label="Hourly focus time distribution throughout the day">
            <FocusDistribution data={transformedData.hourlyDistribution} />
          </CardContent>
        </Card>
      </div>

      {/* Daily Patterns Section */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card className="bg-gradient-to-br from-chart-5/[0.02] to-chart-5/[0.05] bg-card border-border shadow-lg md:col-span-2">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg font-semibold flex items-center text-foreground">
              <div className="mr-3 p-2 rounded-lg bg-chart-5/10 border border-border">
                <Activity className="h-4 w-4 text-chart-5" />
              </div>
              Daily Focus Heatmap
            </CardTitle>
            <CardDescription className="text-sm text-muted-foreground">Your focus intensity throughout the day</CardDescription>
          </CardHeader>
          <CardContent className="pt-0">
            <DailyHeatmap data={transformedData.dailyHeatmap} />
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-primary/[0.02] to-primary/[0.05] bg-card border-border shadow-lg">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg font-semibold flex items-center text-foreground">
              <div className="mr-3 p-2 rounded-lg bg-primary/10 border border-border">
                <Target className="h-4 w-4 text-primary" />
              </div>
              Today&apos;s Sessions
            </CardTitle>
            <CardDescription className="text-sm text-muted-foreground">Breakdown of today&apos;s focus sessions</CardDescription>
          </CardHeader>
          <CardContent className="h-[280px] overflow-auto pt-0">
            <div className="space-y-3">
              {transformedData.todaySessions.map((session: TodaySession, index: number) => (
                <div
                  key={index}
                  className="flex items-center justify-between rounded-lg bg-accent/30 p-3 transition-colors hover:bg-accent/50"
                >
                  <div>
                    <p className="font-medium text-sm text-foreground">{session.title || `Session ${index + 1}`}</p>
                    <p className="text-xs text-muted-foreground">{formatTimeRange(session.timeRange)}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-bold text-primary" aria-label={`Session duration: ${formatMinutesReadable(session.duration)}`}>
                      {formatMinutesReadable(session.duration)}
                    </p>
                    {session.completed ? (
                      <span className="text-xs text-chart-2 font-medium">Completed</span>
                    ) : (
                      <span className="text-xs text-chart-4 font-medium">Interrupted</span>
                    )}
                  </div>
                </div>
              ))}

              {transformedData.todaySessions.length === 0 && (
                <p className="text-center text-muted-foreground py-8 text-sm">No sessions recorded today</p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Calendar & Insights Section */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card className="bg-gradient-to-br from-chart-2/[0.02] to-chart-2/[0.05] bg-card border-border shadow-lg md:col-span-2">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg font-semibold flex items-center text-foreground">
              <div className="mr-3 p-2 rounded-lg bg-chart-2/10 border border-border">
                <Calendar className="h-4 w-4 text-chart-2" />
              </div>
              Focus Calendar
            </CardTitle>
            <CardDescription className="text-sm text-muted-foreground">Your focus activity over the past 30 days</CardDescription>
          </CardHeader>
          <CardContent className="pt-0">
            <StreakCalendar
              data={transformedData.streakData}
              dailyFocusTime={transformedData.dailyFocusTime}
            />
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-chart-1/[0.02] to-chart-1/[0.05] bg-card border-border shadow-lg">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg font-semibold flex items-center text-foreground">
              <div className="mr-3 p-2 rounded-lg bg-chart-1/10 border border-border">
                <Zap className="h-4 w-4 text-chart-1" />
              </div>
              Focus Insights
            </CardTitle>
            <CardDescription className="text-sm text-muted-foreground">Patterns and recommendations</CardDescription>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="space-y-3">
              <div className="rounded-lg bg-accent/30 p-3">
                <h4 className="mb-2 font-medium text-sm text-foreground">Focus Quality</h4>
                <div className="mb-2 flex items-center">
                  <div className="h-2 w-full rounded-full bg-muted">
                    <div
                      className="h-2 rounded-full bg-chart-2"
                      style={{ width: `${transformedData.todayQuality}%` }}
                    ></div>
                  </div>
                  <span className="ml-2 text-sm font-medium text-foreground">{transformedData.todayQuality}%</span>
                </div>
                <p className="text-xs text-muted-foreground">
                  {transformedData.todayQuality > 80
                    ? "Excellent focus! You completed most sessions without interruptions."
                    : transformedData.todayQuality > 50
                      ? "Good focus, with some interruptions."
                      : "You had several interruptions. Consider adjusting your environment."}
                </p>
              </div>

              <div className="rounded-lg bg-accent/30 p-3">
                <h4 className="mb-2 font-medium text-sm text-foreground">Consistency Score</h4>
                <div className="mb-2 flex items-center">
                  <div className="h-2 w-full rounded-full bg-muted">
                    <div
                      className="h-2 rounded-full bg-chart-4"
                      style={{ width: `${transformedData.consistencyScore}%` }}
                    ></div>
                  </div>
                  <span className="ml-2 text-sm font-medium text-foreground">{transformedData.consistencyScore}%</span>
                </div>
                <p className="text-xs text-muted-foreground">
                  {transformedData.consistencyScore > 80
                    ? "Excellent consistency! You're building a strong focus habit."
                    : transformedData.consistencyScore > 50
                      ? "Good consistency. Try to reduce gaps between active days."
                      : "Consider setting a regular schedule for better consistency."}
                </p>
              </div>

              <div className="rounded-lg bg-accent/30 p-3">
                <h4 className="mb-2 font-medium text-sm text-foreground">Recommendation</h4>
                <p className="text-xs text-muted-foreground">{transformedData.recommendation}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Session Statistics Section */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card className="bg-gradient-to-br from-chart-3/[0.02] to-chart-3/[0.05] bg-card border-border shadow-lg transition-all duration-300 hover:shadow-xl analytics-card-hover">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg font-semibold flex items-center text-foreground">
              <div className="mr-3 p-2 rounded-lg bg-chart-3/10 border border-border">
                <Flame className="h-4 w-4 text-chart-3" />
              </div>
              Streak Analysis
            </CardTitle>
            <CardDescription className="text-sm text-muted-foreground">Your consistency patterns and achievements</CardDescription>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="space-y-4">
              <motion.div
                className="flex items-center justify-between p-3 rounded-lg bg-accent/20 hover:bg-accent/30 transition-colors duration-200"
                {...getAnimationProps(0.1)}
                role="group"
                aria-label="Active days this month metric"
              >
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 rounded-full bg-chart-4" aria-hidden="true"></div>
                  <span className="text-muted-foreground text-sm font-medium">Active Days This Month</span>
                </div>
                <span className="font-bold text-chart-4 text-lg" aria-label={`${transformedData.daysActiveThisMonth} active days this month`}>
                  {transformedData.daysActiveThisMonth} days
                </span>
              </motion.div>

              <motion.div
                className="flex items-center justify-between p-3 rounded-lg bg-accent/20 hover:bg-accent/30 transition-colors duration-200"
                {...getAnimationProps(0.2)}
                role="group"
                aria-label="Peak daily sessions metric"
              >
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 rounded-full bg-chart-2" aria-hidden="true"></div>
                  <span className="text-muted-foreground text-sm font-medium">Peak Daily Sessions</span>
                </div>
                <span className="font-bold text-chart-2 text-lg" aria-label={`${transformedData.streakData.length > 0 ? Math.max(...transformedData.streakData.map(d => d.count)) : 0} peak daily sessions`}>
                  {transformedData.streakData.length > 0 ? Math.max(...transformedData.streakData.map(d => d.count)) : 0} sessions
                </span>
              </motion.div>

              <motion.div
                className="flex items-center justify-between p-3 rounded-lg bg-accent/20 hover:bg-accent/30 transition-colors duration-200"
                {...getAnimationProps(0.3)}
                role="group"
                aria-label="Total focus sessions metric"
              >
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 rounded-full bg-primary" aria-hidden="true"></div>
                  <span className="text-muted-foreground text-sm font-medium">Total Focus Sessions</span>
                </div>
                <span className="font-bold text-primary text-lg" aria-label={`${safeStatsData.focusSessions} total focus sessions`}>
                  {safeStatsData.focusSessions}
                </span>
              </motion.div>

              <motion.div
                className="flex items-center justify-between p-3 rounded-lg bg-accent/20 hover:bg-accent/30 transition-colors duration-200"
                {...getAnimationProps(0.4)}
                role="group"
                aria-label="Completed sessions metric"
              >
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 rounded-full bg-chart-1" aria-hidden="true"></div>
                  <span className="text-muted-foreground text-sm font-medium">Completed Sessions</span>
                </div>
                <span className="font-bold text-chart-1 text-lg" aria-label={`${safeStatsData.completedSessions} completed sessions`}>
                  {safeStatsData.completedSessions}
                </span>
              </motion.div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-chart-5/[0.02] to-chart-5/[0.05] bg-card border-border shadow-lg transition-all duration-300 hover:shadow-xl analytics-card-hover">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg font-semibold flex items-center text-foreground">
              <div className="mr-3 p-2 rounded-lg bg-chart-5/10 border border-border">
                <Timer className="h-4 w-4 text-chart-5" />
              </div>
              Session Duration
            </CardTitle>
            <CardDescription className="text-sm text-muted-foreground">Time allocation and productivity insights</CardDescription>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="space-y-4">
              <motion.div
                className="rounded-lg bg-gradient-to-r from-chart-1/5 to-chart-1/10 border border-chart-1/20 p-4 hover:shadow-md transition-all duration-200"
                {...getVerticalAnimationProps(0.1)}
                role="group"
                aria-label="Total focus time breakdown"
              >
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-semibold text-sm text-foreground flex items-center">
                    <Clock className="h-3 w-3 mr-1.5 text-chart-1" aria-hidden="true" />
                    Total Focus Time
                  </h4>
                  <div className="text-right">
                    <p className="text-xl font-bold text-chart-1" aria-label={`Total focus time: ${formatDurationReadable(safeStatsData.focusDuration)}`}>
                      {formatDurationReadable(safeStatsData.focusDuration)}
                    </p>
                  </div>
                </div>
                <div className="flex items-center justify-between text-xs">
                  <span className="text-muted-foreground">Across {safeStatsData.focusSessions} sessions</span>
                  <span className="text-chart-1 font-medium" aria-label={`Average session duration: ${formatMinutesReadable(safeStatsData.focusSessions > 0 ? Math.round(safeStatsData.focusDuration / safeStatsData.focusSessions / 60) : 0)}`}>
                    {formatMinutesReadable(safeStatsData.focusSessions > 0 ? Math.round(safeStatsData.focusDuration / safeStatsData.focusSessions / 60) : 0)} avg
                  </span>
                </div>
              </motion.div>

              <motion.div
                className="rounded-lg bg-gradient-to-r from-chart-2/5 to-chart-2/10 border border-chart-2/20 p-4 hover:shadow-md transition-all duration-200"
                {...getVerticalAnimationProps(0.2)}
                role="group"
                aria-label="Break time breakdown"
              >
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-semibold text-sm text-foreground flex items-center">
                    <Target className="h-3 w-3 mr-1.5 text-chart-2" aria-hidden="true" />
                    Break Time
                  </h4>
                  <div className="text-right">
                    <p className="text-lg font-bold text-chart-2" aria-label={`Total break time: ${formatDurationReadable(safeStatsData.shortBreakDuration + safeStatsData.longBreakDuration)}`}>
                      {formatDurationReadable(safeStatsData.shortBreakDuration + safeStatsData.longBreakDuration)}
                    </p>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div className="text-center p-2 rounded bg-accent/20 hover:bg-accent/30 transition-colors duration-200" role="group" aria-label="Short breaks summary">
                    <div className="font-medium text-muted-foreground">Short Breaks</div>
                    <div className="text-chart-2 font-bold" aria-label={`Short breaks: ${formatDurationReadable(safeStatsData.shortBreakDuration)}`}>
                      {formatDurationReadable(safeStatsData.shortBreakDuration)}
                    </div>
                  </div>
                  <div className="text-center p-2 rounded bg-accent/20 hover:bg-accent/30 transition-colors duration-200" role="group" aria-label="Long breaks summary">
                    <div className="font-medium text-muted-foreground">Long Breaks</div>
                    <div className="text-chart-2 font-bold" aria-label={`Long breaks: ${formatDurationReadable(safeStatsData.longBreakDuration)}`}>
                      {formatDurationReadable(safeStatsData.longBreakDuration)}
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Daily Session Timeline Section */}
      <div className="mt-6 overflow-hidden">
        <HorizontalSessionTimeline
          sessions={timelineSessionData || transformedData.dailyCalendarSessions}
          date={selectedTimelineDate || new Date().toISOString().split("T")[0]}
          onDateChange={onTimelineDateChange}
        />
      </div>
    </div>
  )
}
