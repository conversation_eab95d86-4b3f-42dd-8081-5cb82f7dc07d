"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, CartesianGrid, ResponsiveContainer, Tooltip, XAxis, YAxis, LabelList } from "recharts"
import { AlertCircle, Clock, Sun, Moon } from "lucide-react"
import { Badge } from "@/components/ui/badge"

interface FocusDistributionProps {
  data: Array<{
    hour: string
    value: number
  }>
}

interface CustomTooltipProps {
  active?: boolean
  payload?: Array<{ value: number }>
  label?: string
}

export function FocusDistribution({ data }: FocusDistributionProps) {
  // Calculate peak hours and total focus time
  const totalFocusTime = data.reduce((sum, hour) => sum + hour.value, 0)
  
  // Find the peak hour (most productive)
  const peakHour = data.length > 0 
    ? [...data].sort((a, b) => b.value - a.value)[0] 
    : { hour: "0", value: 0 } // Default value if no data
  
  // Format 24-hour time to 12-hour format with AM/PM
  const formatHour = (hour: string | number | undefined) => {
    if (hour === undefined) return '12 AM'
    const hourNum = typeof hour === 'string' ? parseInt(hour, 10) : hour
    if (hourNum === 0) return '12 AM'
    if (hourNum === 12) return '12 PM'
    return hourNum > 12 ? `${hourNum - 12} PM` : `${hourNum} AM`
  }

  // Identify time of day periods
  const morningHours = data.slice(5, 12) // 5 AM to 11 AM
  const afternoonHours = data.slice(12, 17) // 12 PM to 4 PM
  const eveningHours = data.slice(17, 22) // 5 PM to 9 PM
  const nightHours = [...data.slice(22), ...data.slice(0, 5)] // 10 PM to 4 AM

  const morningTotal = morningHours.reduce((sum, hour) => sum + hour.value, 0)
  const afternoonTotal = afternoonHours.reduce((sum, hour) => sum + hour.value, 0)
  const eveningTotal = eveningHours.reduce((sum, hour) => sum + hour.value, 0)
  const nightTotal = nightHours.reduce((sum, hour) => sum + hour.value, 0)

  // Determine the most productive time of day
  const timeOfDayValues = [
    { name: "Morning", value: morningTotal, icon: Sun },
    { name: "Afternoon", value: afternoonTotal, icon: Sun },
    { name: "Evening", value: eveningTotal, icon: Moon },
    { name: "Night", value: nightTotal, icon: Moon }
  ]
  
  const mostProductiveTimeOfDay = [...timeOfDayValues].sort((a, b) => b.value - a.value)[0]

  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }: CustomTooltipProps) => {
    if (active && payload && payload.length) {
      return (
        <div className="rounded-lg border border-border bg-card p-3 shadow-md backdrop-blur-sm" role="tooltip">
          <p className="mb-1 font-medium text-foreground">{formatHour(label)}</p>
          <p className="text-chart-4">
            <span className="font-bold">{payload[0].value}</span> minutes
          </p>
          {payload[0].value > 0 && totalFocusTime > 0 && (
            <div className="mt-1 pt-1 border-t border-border">
              <p className="text-xs text-muted-foreground">
                {Math.round((payload[0].value / totalFocusTime) * 100)}% of total focus time
              </p>
            </div>
          )}
        </div>
      )
    }
    return null
  }

  // Custom label formatter for bars
  const renderCustomLabel = (props: any) => {
    const { x, y, width, value } = props
    if (!value || value === 0) return null

    return (
      <text
        x={x + width / 2}
        y={y - 4}
        fill="currentColor"
        className="text-muted-foreground"
        textAnchor="middle"
        fontSize={10}
        fontWeight={500}
        aria-hidden="true"
      >
        {value}
      </text>
    )
  }

  return (
    <div className="space-y-4">
      {/* Summary stats */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
        {/* Peak focus hour */}
        <div className="p-3 rounded-lg bg-accent/30 hover:bg-accent/40 transition-colors" tabIndex={0}>
          <div className="flex items-center justify-between">
            <p className="text-xs text-muted-foreground">Peak Hour</p>
            <Badge variant="outline" className="text-[10px] h-4 px-1">
              {formatHour(peakHour.hour)}
            </Badge>
          </div>
          <p className="text-lg font-bold text-chart-4 mt-1" aria-label={`${peakHour.value} minutes at ${formatHour(peakHour.hour)}`}>
            {peakHour.value} min
          </p>
          <div className="flex items-center mt-1">
            <Clock className="h-3 w-3 mr-1 text-muted-foreground" aria-hidden="true" />
            <span className="text-xs text-muted-foreground">Most productive hour</span>
          </div>
        </div>

        {/* Most productive time of day */}
        <div className="p-3 rounded-lg bg-accent/30 hover:bg-accent/40 transition-colors" tabIndex={0}>
          <div className="flex items-center justify-between">
            <p className="text-xs text-muted-foreground">Best Time of Day</p>
            <Badge variant="outline" className="text-[10px] h-4 px-1 flex items-center gap-1">
              {mostProductiveTimeOfDay.name === "Morning" || mostProductiveTimeOfDay.name === "Afternoon" 
                ? <Sun className="h-2.5 w-2.5" aria-hidden="true" /> 
                : <Moon className="h-2.5 w-2.5" aria-hidden="true" />
              }
              {mostProductiveTimeOfDay.name.toUpperCase()}
            </Badge>
          </div>
          <p className="text-lg font-bold text-chart-4 mt-1" aria-label={`${mostProductiveTimeOfDay.value} minutes during ${mostProductiveTimeOfDay.name}`}>
            {mostProductiveTimeOfDay.value} min
          </p>
          <div className="flex items-center mt-1">
            <span className="text-xs text-muted-foreground">
              {totalFocusTime > 0 ? `${Math.round((mostProductiveTimeOfDay.value / totalFocusTime) * 100)}% of focus time` : 'No focus time recorded'}
            </span>
          </div>
        </div>
      </div>

      {/* Hourly distribution chart */}
      <div 
        className="h-[220px] sm:h-[250px] w-full" 
        role="region" 
        aria-label="Hourly distribution of focus time throughout the day"
      >
        {totalFocusTime > 0 ? (
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={data} margin={{ top: 20, right: 5, left: -15, bottom: 20 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" vertical={false} />
              <XAxis
                dataKey="hour"
                stroke="currentColor"
                className="text-muted-foreground"
                tickLine={false}
                axisLine={false}
                tick={{ fontSize: 11 }}
                tickFormatter={formatHour}
                interval={2}
                angle={-45}
                textAnchor="end"
                height={60}
                padding={{ left: 5, right: 5 }}
                aria-label="Hours of the day"
              />
              <YAxis
                stroke="currentColor"
                className="text-muted-foreground"
                tickLine={false}
                axisLine={false}
                tick={{ fontSize: 11 }}
                width={30}
                label={{ value: 'minutes', position: 'top', offset: 15, style: { fontSize: 11, fill: 'currentColor', textAnchor: 'middle' } }}
                aria-label="Focus minutes"
              />
              <Tooltip content={<CustomTooltip />} />
              <Bar
                dataKey="value"
                fill="#36A3F2"
                radius={[4, 4, 0, 0]}
                isAnimationActive={true}
                animationDuration={1000}
                animationEasing="ease-out"
                name="Minutes"
              >
                <LabelList content={renderCustomLabel} />
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        ) : (
          <div className="h-full flex items-center justify-center flex-col p-4">
            <AlertCircle className="h-8 w-8 text-muted-foreground mb-2" aria-hidden="true" />
            <p className="text-sm font-medium text-muted-foreground">No focus time data available</p>
            <p className="text-xs text-muted-foreground mt-2 text-center">
              Complete some focus sessions to see your hourly distribution patterns
            </p>
          </div>
        )}
      </div>
    </div>
  )
}
