'use client';

import { useRef, useState, useEffect, memo, useMemo, useCallback } from 'react';
import { usePomodoroStore, type Video } from '@/lib/pomodoro-store';
import { useAudioStore } from '@/lib/audio-store';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Loader2, AlertCircle } from 'lucide-react';
import Link from 'next/link';
import { FallbackThumbnail } from './fallback-thumbnail';
import { AudioPlayer } from '../../../components/audio/audio-player';
import { motion } from 'framer-motion';

interface VideoBackgroundProps {
  onBackNavigation?: () => void;
}

// Then export the VideoBackground as a memoized component
export const VideoBackground = memo(function VideoBackground({ onBackNavigation }: VideoBackgroundProps) {
  const selectedVideo = usePomodoroStore((state) => state.selectedVideo);
  const isVideoSwitching = usePomodoroStore((state) => state.isVideoSwitching);
  const { useVideoDefaultAudio, selectedAudios } = useAudioStore();

  const videoRef = useRef<HTMLVideoElement>(null);
  const [isInitializing, setIsInitializing] = useState(true);
  const [isError, setIsError] = useState(false);
  const [thumbnailError, setThumbnailError] = useState(false);
  const [thumbnailLoaded, setThumbnailLoaded] = useState(false);
  const [displayedThumbnailUrl, setDisplayedThumbnailUrl] = useState<string>('');
  const [videoMuted, setVideoMuted] = useState(false);
  // States for improved transitions
  const [videoReady, setVideoReady] = useState(false);
  const [showThumbnail, setShowThumbnail] = useState(true);
  const [videoOpacity, setVideoOpacity] = useState(0);
  // States for improved loading
  const [hasPlaybackStarted, setHasPlaybackStarted] = useState(false);
  const playAttemptRef = useRef<NodeJS.Timeout | null>(null);
  // States for seamless video switching
  const [nextVideoRef, setNextVideoRef] = useState<HTMLVideoElement | null>(null);
  const [nextVideoReady, setNextVideoReady] = useState(false);
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  // Function to preload next video and handle seamless transition
  const preloadNextVideo = useCallback((nextVideo: Video) => {
    // Create a new video element for preloading
    const preloadVideo = document.createElement('video');
    preloadVideo.src = nextVideo.src;
    preloadVideo.autoplay = false;
    preloadVideo.loop = true;
    preloadVideo.muted = videoMuted;
    preloadVideo.playsInline = true;
    preloadVideo.preload = 'auto';

    // Preload thumbnail
    const preloadThumbnail = new Image();
    preloadThumbnail.src = nextVideo.thumbnail;

    let thumbnailLoaded = false;
    let videoLoaded = false;

    const checkIfReady = () => {
      if (thumbnailLoaded && videoLoaded) {
        // Both are ready, switch to new video
        setNextVideoRef(preloadVideo);
        setNextVideoReady(true);

        // Update the store to switch video
        usePomodoroStore.getState().selectVideo(nextVideo);
      }
    };

    preloadThumbnail.onload = () => {
      thumbnailLoaded = true;
      checkIfReady();
    };

    preloadThumbnail.onerror = () => {
      thumbnailLoaded = true; // Continue even with thumbnail error
      checkIfReady();
    };

    preloadVideo.oncanplay = () => {
      if (preloadVideo.readyState >= 3) {
        videoLoaded = true;
        checkIfReady();
      }
    };

    preloadVideo.onerror = () => {
      // Handle video error - fallback to normal switching
      usePomodoroStore.getState().setVideoSwitching(false);
      usePomodoroStore.getState().selectVideo(nextVideo);
    };
  }, [videoMuted]);

  useEffect(() => {
    // Reset loading state when video changes
    if (selectedVideo) {
      // Check if this is a seamless video switch
      if (nextVideoReady && nextVideoRef) {
        // This is a seamless switch - use preloaded video
        setVideoReady(true);
        setThumbnailLoaded(true);
        setDisplayedThumbnailUrl(selectedVideo.thumbnail);
        setIsInitializing(false);
        usePomodoroStore.getState().setVideoSwitching(false);

        // Reset next video states
        setNextVideoRef(null);
        setNextVideoReady(false);

        // For seamless switching, keep video visible (don't show thumbnail)
        setShowThumbnail(false);
        setVideoOpacity(1);
        setIsInitialLoad(false);
      } else {
        // This is a normal video load (initial or fallback)
        setIsInitializing(true);
        setIsError(false);
        setThumbnailError(false);
        setThumbnailLoaded(false);
        setVideoReady(false);
        setDisplayedThumbnailUrl('');
        setHasPlaybackStarted(false);

        // For initial loads, show thumbnail first
        if (isInitialLoad) {
          setShowThumbnail(true);
          setVideoOpacity(0);
        }
      }

      // Capture the current timeout ID to use in cleanup
      const currentTimeoutId = playAttemptRef.current;

      // Clear any existing play attempt timeout
      if (currentTimeoutId) {
        clearTimeout(currentTimeoutId);
      }
    }
  }, [selectedVideo, nextVideoReady, nextVideoRef, isInitialLoad]);

  // Add cleanup effect when component unmounts
  useEffect(() => {
    return () => {
      // Clear displayed thumbnail URL when component unmounts
      setDisplayedThumbnailUrl('');
    };
  }, []);

  // Pre-load the thumbnail image and handle successful loading
  useEffect(() => {
    if (selectedVideo && !nextVideoReady) {
      // Only load thumbnail if this is not a seamless video switch
      const img = new Image();
      img.src = selectedVideo.thumbnail;

      img.onload = () => {
        setThumbnailLoaded(true);
        setThumbnailError(false);
        // Only show thumbnail for initial loads, not seamless switches
        if (isInitialLoad) {
          setShowThumbnail(true);
          setVideoOpacity(0);
        }
        // Update the displayed thumbnail URL only after successful loading
        setDisplayedThumbnailUrl(selectedVideo.thumbnail);
      };

      img.onerror = () => {
        console.error('Error loading thumbnail image');
        setThumbnailError(true);
        setThumbnailLoaded(true); // Still set to true to continue flow
        // Only show thumbnail layer for initial loads
        if (isInitialLoad) {
          setShowThumbnail(true);
          setVideoOpacity(0);
        }
      };
    }
  }, [selectedVideo, nextVideoReady, isInitialLoad]);

  // Update initialization state based on thumbnail loading
  useEffect(() => {
    if (thumbnailLoaded && selectedVideo) {
      // Don't set isInitializing to false until video is also ready
      // This will be handled in a separate effect that checks both conditions
    }
  }, [thumbnailLoaded, selectedVideo]);

  // New effect to handle when both thumbnail and video are ready
  useEffect(() => {
    if (thumbnailLoaded && videoReady && selectedVideo) {
      // Short delay to ensure smooth transition
      const delay = setTimeout(() => {
        setIsInitializing(false);
      }, 300);

      return () => clearTimeout(delay);
    }
  }, [thumbnailLoaded, videoReady, selectedVideo]);

  // New effect to handle video playback attempts
  useEffect(() => {
    if (videoRef.current && videoReady && !hasPlaybackStarted) {
      const playVideo = () => {
        if (videoRef.current) {
          videoRef.current.play()
            .then(() => {
              setHasPlaybackStarted(true);
            })
            .catch(error => {
              // If autoplay was prevented (common in browsers)
              console.warn('Video playback was prevented:', error);

              // Try again after user interacts with page
              const handleUserInteraction = () => {
                if (videoRef.current) {
                  videoRef.current.play()
                    .then(() => {
                      setHasPlaybackStarted(true);
                      // Remove event listeners once playback starts
                      document.removeEventListener('click', handleUserInteraction);
                      document.removeEventListener('keydown', handleUserInteraction);
                      document.removeEventListener('touchstart', handleUserInteraction);
                    })
                    .catch(() => {
                      // Keep listeners if playback fails
                    });
                }
              };

              document.addEventListener('click', handleUserInteraction);
              document.addEventListener('keydown', handleUserInteraction);
              document.addEventListener('touchstart', handleUserInteraction);
            });
        }
      };

      // Try to play video immediately
      playVideo();
    }

    // Capture the current timeout ID to use in cleanup
    const currentTimeoutId = playAttemptRef.current;

    return () => {
      // Clear any existing play attempt timeout on cleanup
      if (currentTimeoutId) {
        clearTimeout(currentTimeoutId);
      }
    };
  }, [videoReady, hasPlaybackStarted]);

  // Mute video if using custom audio tracks
  useEffect(() => {
    if (videoRef.current) {
      const shouldMuteVideo = !useVideoDefaultAudio && selectedAudios.length > 0;
      videoRef.current.muted = shouldMuteVideo;
      setVideoMuted(shouldMuteVideo);
    }
  }, [useVideoDefaultAudio, selectedAudios.length]);

  // Auto-play audio when video is ready
  useEffect(() => {
    if (!isInitializing && !isError && !useVideoDefaultAudio && selectedAudios.length > 0) {
      // Find audio element and play it
      const audioElements = document.querySelectorAll('audio');
      if (audioElements.length > 0) {
        const audioElement = audioElements[0];
        // Try to play it when the user interacts with the page
        const handleUserInteraction = () => {
          audioElement.play()
            .then(() => {
              // Remove event listeners once playback starts
              document.removeEventListener('click', handleUserInteraction);
              document.removeEventListener('keydown', handleUserInteraction);
              document.removeEventListener('touchstart', handleUserInteraction);
            })
            .catch(() => {
              // Keep listeners if playback fails
            });
        };

        // First attempt to play
        audioElement.play().catch(() => {
          // If initial play fails, add listeners for user interaction
          document.addEventListener('click', handleUserInteraction);
          document.addEventListener('keydown', handleUserInteraction);
          document.addEventListener('touchstart', handleUserInteraction);
        });
      }
    }
  }, [isInitializing, isError, useVideoDefaultAudio, selectedAudios]);

  // Add a continuous check for video readiness to ensure smoother transitions
  useEffect(() => {
    if (videoRef.current && !videoReady && !isError) {
      // Set up a progress checker that runs every 200ms
      const progressChecker = setInterval(() => {
        if (videoRef.current && videoRef.current.readyState >= 3) { // HAVE_FUTURE_DATA or higher
          setVideoReady(true);
          clearInterval(progressChecker);
        }
      }, 200);

      return () => clearInterval(progressChecker);
    }
  }, [videoReady, isError]);

  // Modified effect for thumbnail-to-video transition
  useEffect(() => {
    if (videoReady && !isError && thumbnailLoaded) {
      // Only start fading in the video when it's truly ready to be shown
      // First make sure the video is actually loaded and ready
      if (videoRef.current && videoRef.current.readyState >= 3) { // HAVE_FUTURE_DATA or higher
        // For initial loads, hide thumbnail and show video
        if (isInitialLoad) {
          setShowThumbnail(false);
          setVideoOpacity(1);
        } else {
          // For seamless switches, video should already be visible
          setVideoOpacity(1);
        }
      }
    }
  }, [videoReady, isError, thumbnailLoaded, isInitialLoad]);

  // Effect to handle seamless video switching when next video is ready
  useEffect(() => {
    if (nextVideoReady && nextVideoRef && selectedVideo) {
      // Replace the current video element's source with the preloaded video
      if (videoRef.current) {
        // Copy the preloaded video's properties to the main video element
        videoRef.current.src = nextVideoRef.src;
        videoRef.current.currentTime = 0;

        // Start playing the new video
        videoRef.current.play()
          .then(() => {
            setHasPlaybackStarted(true);
            // Clean up the preloaded video element
            if (nextVideoRef.parentNode) {
              nextVideoRef.parentNode.removeChild(nextVideoRef);
            }
          })
          .catch((error) => {
            console.warn('Error playing switched video:', error);
          });
      }
    }
  }, [nextVideoReady, nextVideoRef, selectedVideo]);

  if (!selectedVideo) {
    return null;
  }

  const handleCanPlay = () => {
    // Only mark video as ready when it can actually play without interruption
    if (videoRef.current && videoRef.current.readyState >= 3) { // HAVE_FUTURE_DATA or higher
      setVideoReady(true);
    }
  };

  const handleLoadedData = () => {
    // Video has loaded its first frame
    // Check if we have enough data for smooth playback
    if (videoRef.current && videoRef.current.readyState >= 3) { // HAVE_FUTURE_DATA or higher
      setVideoReady(true);
    }
  };

  const handlePlaying = () => {
    // Video is now playing
    setHasPlaybackStarted(true);
  };

  const handleVideoError = () => {
    setIsInitializing(false);
    setIsError(true);
  };

  const hasCustomAudio = !useVideoDefaultAudio && selectedAudios.length > 0;

  return (
    <div className="fixed inset-0 bg-black z-0" data-supportsscroll="false">
      {/* Thumbnail layer - always render but control visibility with opacity */}
      <div
        className={`absolute inset-0 z-10 ${
          showThumbnail && displayedThumbnailUrl ? 'opacity-100' : 'opacity-0'
        }`}
        style={{
          transition: showThumbnail ? 'none' : 'opacity 0ms'
        }}
      >
        {!thumbnailError && displayedThumbnailUrl ? (
          <div
            className="relative w-full h-full"
            style={{
              backgroundImage: `url(${displayedThumbnailUrl})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
            }}
          />
        ) : displayedThumbnailUrl && thumbnailError ? (
          <div className="absolute inset-0">
            <FallbackThumbnail title={selectedVideo.title} />
          </div>
        ) : null}
      </div>

      {/* Video switching loading indicator */}
      {isVideoSwitching && !isError && (
        <div className="absolute inset-0 flex items-center justify-center z-30">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.2 }}
            className="bg-neutral-300/20 hover:bg-neutral-300/30 dark:bg-neutral-400/20 dark:hover:bg-neutral-400/30 text-neutral-600 dark:text-neutral-300 backdrop-blur-[1px] border border-neutral-400/20 px-4 py-3 rounded-lg flex items-center gap-3"
          >
            <Loader2 className="h-4 w-4 text-white text-sm font-semibold animate-spin" />
            <span
              className="text-white text-sm font-semibold"
              style={{
                textShadow: '0 2px 4px rgba(0, 0, 0, 0.4), 0 1px 2px rgba(0, 0, 0, 0.3)'
              }}
            >
              Loading new scenery...
            </span>
          </motion.div>
        </div>
      )}

      {/* Error overlay */}
      {isError && (
        <div className="absolute inset-0 flex flex-col items-center justify-center p-4 sm:p-8 z-30">
          <div className="bg-black/50 backdrop-blur-lg p-4 sm:p-8 rounded-2xl flex flex-col items-center max-w-md border border-red-500/20 text-center">
            <div className="rounded-full bg-red-500/10 p-3 sm:p-4 mb-3 sm:mb-4">
              <AlertCircle className="h-6 w-6 sm:h-8 sm:w-8 text-red-500" />
            </div>
            <h3 className="text-white text-lg sm:text-xl font-semibold mb-2">Video Error</h3>
            <p className="text-white/70 mb-4 sm:mb-6 text-sm sm:text-base">
              The video &ldquo;{selectedVideo.title}&rdquo; could not be loaded. Please check the file path or try a different background.
            </p>
            {onBackNavigation ? (
              <motion.div
                className="w-full"
                whileHover={{ scale: 1.03 }}
                whileTap={{ scale: 0.97 }}
                transition={{ type: "spring", stiffness: 400, damping: 17 }}
              >
                <Button
                  variant="default"
                  className="w-full bg-gradient-to-r from-red-500 to-orange-500 border-0 transition-all duration-300 h-11 sm:h-12"
                  onClick={onBackNavigation}
                >
                  <ArrowLeft className="mr-2 h-4 w-4" /> Return to Home
                </Button>
              </motion.div>
            ) : (
              <motion.div
                className="w-full relative z-50 pointer-events-auto"
                whileHover={{ scale: 1.03 }}
                whileTap={{ scale: 0.97 }}
                transition={{ type: "spring", stiffness: 400, damping: 17 }}
              >
                <Link href="/" className="w-full block">
                  <Button variant="default" className="w-full bg-gradient-to-r from-red-500 to-orange-500 border-0 transition-all duration-300 h-11 sm:h-12">
                    <ArrowLeft className="mr-2 h-4 w-4" /> Return to Home
                  </Button>
                </Link>
              </motion.div>
            )}
          </div>
        </div>
      )}

      {/* Actual video with immediate opacity transition */}
      <video
        ref={videoRef}
        src={selectedVideo.src}
        autoPlay
        loop
        muted={videoMuted}
        playsInline
        preload="auto"
        className="absolute inset-0 w-full h-full object-cover pointer-events-none"
        style={{ opacity: videoOpacity }}
        onCanPlay={handleCanPlay}
        onLoadedData={handleLoadedData}
        onPlaying={handlePlaying}
        onError={handleVideoError}
        poster={selectedVideo.thumbnail}
      />

      {/* Main audio player for selected audios - hidden but functional */}
      {hasCustomAudio && <AudioPlayer className="hidden" />}
    </div>
  );
});