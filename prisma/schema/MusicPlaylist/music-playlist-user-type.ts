import { z } from "zod";

// User create schema - only basic fields for user playlists
export const createMusicPlaylistUserSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
  imageUrl: z
    .string()
    .transform((value) => {
      // Handle empty strings, "undefined" strings, and null/undefined values
      if (!value || value === "" || value === "undefined" || value === "null") {
        return undefined;
      }
      return value;
    })
    .optional()
    .refine((value) => {
      // Only validate as URL if value exists and is not empty
      if (!value) return true;
      try {
        new URL(value);
        return true;
      } catch {
        return false;
      }
    }, {
      message: "Please enter a valid URL"
    }),
  musicIds: z.array(z.string()).optional(),
  natureSoundIds: z.array(z.string()).optional(),
  videoIds: z.array(z.string()).optional(),
});

// User update schema - allow updating basic fields
export const updateMusicPlaylistUserSchema = z.object({
  name: z.string().min(1, "Name is required").optional(),
  description: z.string().optional(),
  imageUrl: z
    .string()
    .url()
    .transform((value) => (value === "" ? undefined : value))
    .optional(),
  musicIds: z.preprocess(
    (val) => Array.isArray(val) ? val : typeof val === 'string' ? [val] : val,
    z.array(z.string()).optional()
  ),
  natureSoundIds: z.preprocess(
    (val) => Array.isArray(val) ? val : typeof val === 'string' ? [val] : val,
    z.array(z.string()).optional()
  ),
  videoIds: z.preprocess(
    (val) => Array.isArray(val) ? val : typeof val === 'string' ? [val] : val,
    z.array(z.string()).optional()
  ),
});

export type CreateMusicPlaylistUserInput = z.infer<typeof createMusicPlaylistUserSchema>;
export type UpdateMusicPlaylistUserInput = z.infer<typeof updateMusicPlaylistUserSchema>;