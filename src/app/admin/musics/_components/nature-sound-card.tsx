"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { MoreHorizontal, Play, PauseCircle } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { useRouter } from "next/navigation";
import { useDeleteNatureSound, useUpdateNatureSound } from "@schemas/Natural/nature-sound-query";
import { toast } from "sonner";
import { GetNatureSounds_ResponseTypeSuccess } from "@schemas/Natural/nature-sound-query";
import { useState } from "react";
import { NatureSoundFormSheet } from "./nature-sound-form-sheet";
import { ConfirmationDialog } from "@/components/ui/confirmation-dialog";
import { useAudioStore } from "@/lib/audio-store";
import { Table<PERSON>ell, TableRow } from "@/components/ui/table";
import { formatDate } from "@/lib/utils";

interface NatureSoundRowProps {
  natureSound: GetNatureSounds_ResponseTypeSuccess[number];
}

export function NatureSoundRow({ natureSound }: NatureSoundRowProps) {
  const router = useRouter();
  const [isEditSheetOpen, setIsEditSheetOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const { mutateAsync: deleteNatureSound, isPending: isDeleting } = useDeleteNatureSound();
  const { mutateAsync: updateNatureSound } = useUpdateNatureSound();
  const { selectedAudioId, setSelectedAudioId } = useAudioStore();

  const isPlaying = selectedAudioId === natureSound.id;

  const handleDelete = async () => {
    try {
      await deleteNatureSound({ id: natureSound.id });
      toast.success("Nature sound deleted successfully");
      setIsDeleteDialogOpen(false);
      router.refresh();
    } catch {
      toast.error("Failed to delete nature sound");
    }
  };

  const handlePlayToggle = () => {
    if (isPlaying) {
      setSelectedAudioId("");
    } else {
      setSelectedAudioId(natureSound.id);
    }
  };

  const handleFeaturedToggle = async () => {
    try {
      await updateNatureSound({
        form: {
          isFeatured: (!(natureSound as any).isFeatured).toString(),
        },
        param: { id: natureSound.id },
      });
      toast.success(`Nature sound ${(natureSound as any).isFeatured ? 'unfeatured' : 'featured'} successfully`);
      router.refresh();
    } catch {
      toast.error("Failed to update featured status");
    }
  };

  return (
    <>
      <TableRow>
        <TableCell className="font-medium">{natureSound.title}</TableCell>
        <TableCell>
          {natureSound.category && natureSound.category.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {natureSound.category.map((cat, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {cat}
                </Badge>
              ))}
            </div>
          )}
        </TableCell>
        <TableCell>{formatDate(natureSound.createdAt)}</TableCell>
        <TableCell>{natureSound.isPublic ? "Public" : "Private"}</TableCell>
        <TableCell>
          <Switch
            checked={(natureSound as any).isFeatured || false}
            onCheckedChange={handleFeaturedToggle}
            size="sm"
          />
        </TableCell>
        <TableCell>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={handlePlayToggle}
              className="h-8 w-8"
            >
              {isPlaying ? (
                <PauseCircle className="h-5 w-5" />
              ) : (
                <Play className="h-5 w-5" />
              )}
            </Button>

            <DropdownMenu modal={false}>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => setIsEditSheetOpen(true)}>
                  Edit
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => setIsDeleteDialogOpen(true)}
                  className="text-destructive cursor-pointer focus:text-destructive focus:bg-destructive/10"
                >
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </TableCell>
      </TableRow>

      <NatureSoundFormSheet
        open={isEditSheetOpen}
        onOpenChange={setIsEditSheetOpen}
        natureSoundId={natureSound.id}
      />

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        title="Delete Nature Sound"
        description={`Are you sure you want to delete "${natureSound.title}"? This action cannot be undone.`}
        onConfirm={handleDelete}
        isLoading={isDeleting}
      />
    </>
  );
}