// Force dynamic behavior at the route segment level
export const dynamic = 'force-dynamic';
export const revalidate = 0;

import prisma from '@/lib/prisma';
// import { MainHeader } from './_components/main-header';
import { HeroSection } from './_components/hero-section';
import { VideoGridSection } from './_components/video-grid-section';
import { FeaturesSection } from './_components/features-section';
// import { MobileDownloadSection } from './_components/mobile-download-section';
import { Footer } from './_components/footer';
import { mapPrismaVideoToVideo } from '@/lib/prisma-types';
import { Suspense } from 'react';
import { VideosSkeleton } from './_components/videos-skeleton';
// import { IOSCompatibilityChecker } from './_components/ios-compatibility-checker';

export default async function Home() {
  // Fetch videos directly from Prisma
  const prismaVideos = await prisma.video.findMany({
    where: {
      isPublic: true,
    },
    include: {
      musicPlaylist: {
        include: {
          musics: true,
        },
      },
      naturePlaylist: {
        include: {
          natureSounds: true,
        },
      },
    },
    orderBy: {
      order: 'asc',
    },
  });
  
  // Map Prisma videos to our Video type
  const videos = prismaVideos.map(mapPrismaVideoToVideo);
  
  return (
    <main className="min-h-screen flex flex-col bg-background dark:bg-slate-950">
      {/* Main Navigation Header */}
      {/* <MainHeader /> */}

      {/* iOS Compatibility Checker - runs tests in background */}
      {/* <IOSCompatibilityChecker /> */}

      {/* Hero Section with Suspense boundary */}
      <Suspense fallback={<VideosSkeleton type="hero" />}>
        <HeroSection videos={videos} />
      </Suspense>

      {/* Background Videos Section with Suspense boundary */}
      <Suspense fallback={<VideosSkeleton type="grid" />}>
        <VideoGridSection videos={videos} />
      </Suspense>

      {/* Features - open by default for better UX */}
      <FeaturesSection />

      {/* Mobile App Download Section */}
      {/* <MobileDownloadSection /> */}

      {/* Footer */}
      <Footer />
    </main>
  );
}
