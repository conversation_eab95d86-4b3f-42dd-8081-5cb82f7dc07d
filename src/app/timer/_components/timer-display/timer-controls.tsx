'use client';

import { cn } from '@/lib/utils';
import PlayPauseButton from './controls/play-pause-button';
import { TimerColorPreset } from '@/lib/pomodoro-store';

interface TimerControlsProps {
  showControls: boolean;
  isDragging: boolean;
  isRunning: boolean;
  timerSize: { width?: number; timeScale?: number } | null;
  timerColor: TimerColorPreset;
  onTimerToggle: () => void;
  onReset: () => void;
}

export function TimerControls({
  showControls,
  isDragging,
  isRunning,
  timerSize,
  timerColor,
  onTimerToggle,
  onReset
}: TimerControlsProps) {
  return (
    <div
      className={cn(
        "flex justify-center items-center",
        "timer-control-fade",
        showControls && !isDragging ? "show mt-2 mb-2" : "hide pointer-events-none h-0 overflow-hidden"
      )}
      style={{
        marginTop: showControls && timerSize?.timeScale && timerSize.timeScale > 1.3
          ? `${Math.min(2, (timerSize.timeScale - 1) * 2)}rem`
          : showControls ? '0.6rem' : '0',
        marginBottom: showControls ? undefined : '0',
        animationDelay: '60ms'
      }}
    >
      <PlayPauseButton
        isRunning={isRunning}
        onClick={onTimerToggle}
        onReset={onReset}
        timerColor={timerColor}
      />
    </div>
  );
}
