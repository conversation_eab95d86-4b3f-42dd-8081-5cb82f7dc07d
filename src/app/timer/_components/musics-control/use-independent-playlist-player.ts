'use client';

import { useState, useRef, useCallback, useEffect } from 'react';
import { PlaybackMode } from './types';
import { useGlobalMusicControl } from '@/lib/use-global-music-control';

// Types for the independent playlist player
interface PlaylistTrack {
  id: string;
  title: string;
  src?: string | null;
  genres?: string[];
}

interface IndependentPlaylist {
  id: string;
  name: string;
  musics: PlaylistTrack[];
  natureSounds: Array<{
    id: string;
    title: string;
    src?: string | null;
    category?: string[];
  }>;
}

// Load settings from localStorage
const loadPlaylistPlayerSettings = () => {
  if (typeof window !== 'undefined') {
    try {
      const settings = localStorage.getItem('independentPlaylistPlayerSettings');
      if (settings) {
        return JSON.parse(settings);
      }
    } catch (e) {
      console.error('Failed to load playlist player settings:', e);
    }
  }
  return {
    volume: 70,
    isMuted: false,
    playbackMode: PlaybackMode.LOOP_ALL
  };
};

// Save settings to localStorage
const savePlaylistPlayerSettings = (settings: any) => {
  if (typeof window !== 'undefined') {
    try {
      localStorage.setItem('independentPlaylistPlayerSettings', JSON.stringify(settings));
    } catch (e) {
      console.error('Failed to save playlist player settings:', e);
    }
  }
};

/**
 * Independent playlist player hook that operates completely separate from global audio control
 */
export function useIndependentPlaylistPlayer(playlist: IndependentPlaylist | null) {
  // Load initial settings
  const initialSettings = loadPlaylistPlayerSettings();
  
  // Audio state
  const [currentMusicIndex, setCurrentMusicIndex] = useState(0);
  const [isMainPlaying, setIsMainPlaying] = useState(false);
  const [mainVolume, setMainVolume] = useState(initialSettings.volume);
  const [isMainMuted, setIsMainMuted] = useState(initialSettings.isMuted);
  const [playbackMode, setPlaybackMode] = useState<PlaybackMode>(initialSettings.playbackMode);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  
  // Audio element ref
  const mainAudioRef = useRef<HTMLAudioElement | null>(null);
  const currentPlaylistIdRef = useRef<string | null>(null);
  const handleTrackEndedRef = useRef<(() => void) | null>(null);

  // Use a stable sourceId that doesn't change with playlist
  const stableSourceId = useRef(`independent-playlist-player-${Date.now()}`);

  // Global music control integration for mutual exclusion with YouTube player
  const globalMusicControl = useGlobalMusicControl({
    sourceId: stableSourceId.current,
    sourceType: 'playlist',
    sourceName: `My Playlist Player (${playlist?.name || 'No Playlist'})`,
    persistOnUnmount: true, // Prevent audio from stopping when UI is hidden
    onPlayStart: () => {
      // This is called when this player gains control
      console.log('Independent Playlist Player: Gained audio control');
    },
    onPause: () => {
      // This is called when another player (like YouTube) takes control
      console.log('Independent Playlist Player: Paused by global control');

      // Pause the music
      if (mainAudioRef.current) {
        mainAudioRef.current.pause();
      }

      // Pause all nature sounds through the parent component
      // We'll need to expose this functionality
      pauseAllNatureSounds();
    },
  });

  // Store global music control in ref to avoid dependency issues
  const globalMusicControlRef = useRef(globalMusicControl);
  globalMusicControlRef.current = globalMusicControl;

  // Function to pause all nature sounds (will be passed from parent)
  const pauseAllNatureSoundsRef = useRef<(() => void) | null>(null);
  const pauseAllNatureSounds = useCallback(() => {
    if (pauseAllNatureSoundsRef.current) {
      pauseAllNatureSoundsRef.current();
    }
  }, []);

  // Initialize audio element
  useEffect(() => {
    if (!mainAudioRef.current) {
      const audio = new Audio();
      audio.preload = 'metadata';

      // Add event listeners
      audio.addEventListener('timeupdate', () => {
        setCurrentTime(audio.currentTime);
      });

      audio.addEventListener('loadedmetadata', () => {
        setDuration(audio.duration || 0);
      });

      audio.addEventListener('ended', () => {
        if (handleTrackEndedRef.current) {
          handleTrackEndedRef.current();
        }
      });

      audio.addEventListener('play', () => {
        setIsMainPlaying(true);
      });

      audio.addEventListener('pause', () => {
        setIsMainPlaying(false);
      });

      mainAudioRef.current = audio;
    }

    return () => {
      if (mainAudioRef.current) {
        mainAudioRef.current.pause();
        mainAudioRef.current.src = '';
        mainAudioRef.current = null;
      }
    };
  }, []);

  // Play specific track
  const playTrack = useCallback((index: number) => {
    if (!playlist?.musics || !playlist.musics[index] || !mainAudioRef.current) return;

    const track = playlist.musics[index];
    if (!track.src) return;

    const audio = mainAudioRef.current;
    const control = globalMusicControlRef.current;

    // Request control before playing
    const canPlay = control.requestControl();
    if (!canPlay) {
      console.log('Independent Playlist Player: Cannot play - no audio control');
      return;
    }

    // Set new source
    audio.src = track.src;
    audio.volume = isMainMuted ? 0 : mainVolume / 100;
    audio.loop = playbackMode === PlaybackMode.LOOP_ONE;

    // Play the track
    audio.play().catch((error) => {
      console.error('Failed to play track:', error);
    });

    setCurrentMusicIndex(index);

    // Notify global control of play start
    control.notifyPlayStart({
      id: track.id,
      title: track.title,
      src: track.src,
      type: 'music',
    });
  }, [playlist, mainVolume, isMainMuted, playbackMode]);

  // Handle track ended
  const handleTrackEnded = useCallback(() => {
    if (!playlist?.musics || playlist.musics.length === 0) return;

    switch (playbackMode) {
      case PlaybackMode.LOOP_ONE:
        // Loop current track (handled by audio.loop property)
        break;
      case PlaybackMode.LOOP_ALL:
        // Move to next track, loop back to first if at end
        const nextIndex = (currentMusicIndex + 1) % playlist.musics.length;
        setCurrentMusicIndex(nextIndex);
        playTrack(nextIndex);
        break;
      case PlaybackMode.SHUFFLE:
        // Play random track
        const randomIndex = Math.floor(Math.random() * playlist.musics.length);
        setCurrentMusicIndex(randomIndex);
        playTrack(randomIndex);
        break;
      default:
        // For any other mode, stop playing
        setIsMainPlaying(false);
        break;
    }
  }, [playlist, currentMusicIndex, playbackMode, playTrack]);

  // Update the ref when handleTrackEnded changes
  useEffect(() => {
    handleTrackEndedRef.current = handleTrackEnded;
  }, [handleTrackEnded]);

  // Initialize playlist
  useEffect(() => {
    if (!playlist || currentPlaylistIdRef.current === playlist.id) return;

    console.log(`Independent Playlist Player: Initializing playlist ${playlist.name} (ID: ${playlist.id})`);

    currentPlaylistIdRef.current = playlist.id;
    setCurrentMusicIndex(0);
    setCurrentTime(0);
    setDuration(0);

    if (playlist.musics && playlist.musics.length > 0) {
      // Load first track but don't auto-play
      const firstTrack = playlist.musics[0];
      if (firstTrack.src && mainAudioRef.current) {
        mainAudioRef.current.src = firstTrack.src;
        mainAudioRef.current.volume = isMainMuted ? 0 : mainVolume / 100;
        mainAudioRef.current.loop = playbackMode === PlaybackMode.LOOP_ONE;
      }
    }
  }, [playlist, mainVolume, isMainMuted, playbackMode]);

  // Note: Control is requested on-demand when playing, not proactively

  // Update audio properties when settings change
  useEffect(() => {
    if (mainAudioRef.current) {
      mainAudioRef.current.volume = isMainMuted ? 0 : mainVolume / 100;
      mainAudioRef.current.loop = playbackMode === PlaybackMode.LOOP_ONE;
    }
  }, [mainVolume, isMainMuted, playbackMode]);

  // Save settings when they change
  useEffect(() => {
    savePlaylistPlayerSettings({
      volume: mainVolume,
      isMuted: isMainMuted,
      playbackMode
    });
  }, [mainVolume, isMainMuted, playbackMode]);

  // Control functions
  const toggleMainPlay = useCallback(() => {
    if (!mainAudioRef.current || !playlist?.musics || playlist.musics.length === 0) return;

    const audio = mainAudioRef.current;
    const control = globalMusicControlRef.current;

    if (isMainPlaying) {
      // Pausing - no need to request control
      audio.pause();
      control.notifyPause();
    } else {
      // Playing - request control first
      const canPlay = control.requestControl();
      if (canPlay) {
        // If no source is set, play the current track
        if (!audio.src && playlist.musics[currentMusicIndex]?.src) {
          playTrack(currentMusicIndex);
        } else {
          audio.play().catch((error) => {
            console.error('Failed to play audio:', error);
          });
        }

        // Notify global control of play start
        const currentTrack = playlist.musics[currentMusicIndex];
        if (currentTrack) {
          control.notifyPlayStart({
            id: currentTrack.id,
            title: currentTrack.title,
            src: currentTrack.src || '',
            type: 'music',
          });
        }
      }
    }
  }, [isMainPlaying, playlist, currentMusicIndex, playTrack]);

  const skipToNext = useCallback(() => {
    if (!playlist?.musics || playlist.musics.length === 0) return;

    const control = globalMusicControlRef.current;
    const canPlay = control.requestControl();
    if (!canPlay) return;

    let nextIndex: number;

    if (playbackMode === PlaybackMode.SHUFFLE) {
      nextIndex = Math.floor(Math.random() * playlist.musics.length);
    } else {
      nextIndex = (currentMusicIndex + 1) % playlist.musics.length;
    }

    playTrack(nextIndex);
  }, [playlist, currentMusicIndex, playbackMode, playTrack]);

  const skipToPrevious = useCallback(() => {
    if (!playlist?.musics || playlist.musics.length === 0) return;

    const control = globalMusicControlRef.current;
    const canPlay = control.requestControl();
    if (!canPlay) return;

    let prevIndex: number;

    if (playbackMode === PlaybackMode.SHUFFLE) {
      prevIndex = Math.floor(Math.random() * playlist.musics.length);
    } else {
      prevIndex = currentMusicIndex === 0 ? playlist.musics.length - 1 : currentMusicIndex - 1;
    }

    playTrack(prevIndex);
  }, [playlist, currentMusicIndex, playbackMode, playTrack]);

  const toggleMainMute = useCallback(() => {
    const newMuted = !isMainMuted;
    setIsMainMuted(newMuted);

    if (mainAudioRef.current) {
      mainAudioRef.current.volume = newMuted ? 0 : mainVolume / 100;
    }
  }, [isMainMuted, mainVolume]);

  const handleMainVolumeChange = useCallback((value: number[]) => {
    const newVolume = value[0];
    setMainVolume(newVolume);

    if (mainAudioRef.current) {
      mainAudioRef.current.volume = isMainMuted ? 0 : newVolume / 100;
    }
  }, [isMainMuted]);

  const togglePlaybackMode = useCallback(() => {
    const modes = [PlaybackMode.LOOP_ALL, PlaybackMode.LOOP_ONE, PlaybackMode.SHUFFLE];
    const currentIndex = modes.indexOf(playbackMode);
    const nextIndex = (currentIndex + 1) % modes.length;
    setPlaybackMode(modes[nextIndex]);
  }, [playbackMode]);

  const handleSeek = useCallback((time: number) => {
    if (!mainAudioRef.current) return;

    mainAudioRef.current.currentTime = time;
    setCurrentTime(time);
  }, []);

  const pauseAll = useCallback(() => {
    if (mainAudioRef.current) {
      mainAudioRef.current.pause();
    }
  }, []);

  // Get current track
  const currentTrack = playlist?.musics?.[currentMusicIndex] || null;

  return {
    // State
    currentTrack,
    currentMusicIndex,
    isMainPlaying,
    mainVolume,
    isMainMuted,
    playbackMode,
    currentTime,
    duration,
    playlist,
    isInitialized: !!playlist,
    musicCount: playlist?.musics?.length || 0,
    canPlay: globalMusicControlRef.current.canPlay, // Use global control state

    // Global control state
    hasGlobalControl: globalMusicControlRef.current.hasControl,
    isGloballyActive: globalMusicControlRef.current.isActive,
    isRegistered: globalMusicControlRef.current.isRegistered,

    // Control functions
    playMainTrack: playTrack,
    toggleMainPlay,
    skipToNext,
    skipToPrevious,
    toggleMainMute,
    handleMainVolumeChange,
    togglePlaybackMode,
    handleSeek,
    pauseAll,

    // Function to set nature sounds pause callback
    setPauseAllNatureSoundsCallback: (callback: () => void) => {
      pauseAllNatureSoundsRef.current = callback;
    },
  };
}
