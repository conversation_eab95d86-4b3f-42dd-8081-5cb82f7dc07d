"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ianG<PERSON>, Legend, ResponsiveContainer, Tooltip, XAxis, <PERSON><PERSON><PERSON><PERSON>, LabelList } from "recharts"
import { <PERSON>Down, ArrowUp, Minus } from "lucide-react"
import { Badge } from "@/components/ui/badge"

interface WeeklyComparisonProps {
  data: Array<{
    name: string
    thisWeek: number
    lastWeek: number
  }>
}

interface CustomTooltipProps {
  active?: boolean
  payload?: Array<{ name: string; value: number; color: string }>
  label?: string
}

export function WeeklyComparison({ data }: WeeklyComparisonProps) {
  // Calculate week-over-week metrics
  const totalThisWeek = data.reduce((sum, day) => sum + day.thisWeek, 0)
  const totalLastWeek = data.reduce((sum, day) => sum + day.lastWeek, 0)
  const weekOverWeekChange = totalLastWeek === 0 
    ? 100 
    : Math.round(((totalThisWeek - totalLastWeek) / totalLastWeek) * 100)
  
  // Find the day with the highest focus time this week
  const highestFocusDay = [...data]
    .sort((a, b) => b.thisWeek - a.thisWeek)[0]
  
  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }: CustomTooltipProps) => {
    if (active && payload && payload.length) {
      return (
        <div className="rounded-lg border border-border bg-card p-3 shadow-md backdrop-blur-sm" role="tooltip">
          <p className="mb-1 font-medium text-foreground">{label}</p>
          {payload.map((entry, index) => (
            <p key={`tooltip-${index}`} style={{ color: entry.color }}>
              <span className="font-medium">{entry.name}: </span>
              <span className="font-bold">{entry.value}</span> minutes
            </p>
          ))}
          {payload.length === 2 && (
            <div className="mt-1 pt-1 border-t border-border">
              <p className="text-xs text-muted-foreground">
                {Number(payload[0].value) > Number(payload[1].value) 
                  ? `+${Number(payload[0].value) - Number(payload[1].value)} minutes vs last week`
                  : Number(payload[0].value) < Number(payload[1].value)
                    ? `-${Number(payload[1].value) - Number(payload[0].value)} minutes vs last week`
                    : `Same as last week`}
              </p>
            </div>
          )}
        </div>
      )
    }
    return null
  }

  // Custom label formatter for bars
  const renderCustomLabel = (props: any) => {
    const { x, y, width, value } = props
    if (!value || value === 0) return null

    return (
      <text
        x={x + width / 2}
        y={y - 4}
        fill="currentColor"
        className="text-muted-foreground"
        textAnchor="middle"
        fontSize={10}
        fontWeight={500}
      >
        {value}
      </text>
    )
  }

  return (
    <div className="space-y-4">
      {/* Stats summary */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mb-2">
        <div className="p-3 rounded-lg bg-accent/30 hover:bg-accent/40 transition-colors" tabIndex={0}>
          <div className="flex items-center justify-between">
            <p className="text-xs text-muted-foreground">This Week</p>
            <Badge variant="outline" className="text-[10px] h-4 px-1">TOTAL</Badge>
          </div>
          <p className="text-lg font-bold text-chart-2 mt-1" aria-label={`${totalThisWeek} minutes this week`}>
            {totalThisWeek} min
          </p>
          <div className="flex items-center mt-1">
            {weekOverWeekChange !== 0 && (
              <>
                {weekOverWeekChange > 0 ? (
                  <ArrowUp className="h-3 w-3 mr-1 text-emerald-500" aria-hidden="true" />
                ) : weekOverWeekChange < 0 ? (
                  <ArrowDown className="h-3 w-3 mr-1 text-primary" aria-hidden="true" />
                ) : (
                  <Minus className="h-3 w-3 mr-1 text-muted-foreground" aria-hidden="true" />
                )}
                <span className={`text-xs font-medium ${
                  weekOverWeekChange > 0 
                    ? "text-emerald-500" 
                    : weekOverWeekChange < 0 
                      ? "text-primary" 
                      : "text-muted-foreground"
                }`} aria-label={`${Math.abs(weekOverWeekChange)}% ${weekOverWeekChange >= 0 ? 'increase' : 'decrease'} from last week`}>
                  {weekOverWeekChange > 0 ? "+" : ""}{weekOverWeekChange}% vs last week
                </span>
              </>
            )}
          </div>
        </div>
        <div className="p-3 rounded-lg bg-accent/30 hover:bg-accent/40 transition-colors" tabIndex={0}>
          <div className="flex items-center justify-between">
            <p className="text-xs text-muted-foreground">Best Day</p>
            <Badge variant="outline" className="text-[10px] h-4 px-1">
              {highestFocusDay.name.toUpperCase()}
            </Badge>
          </div>
          <p className="text-lg font-bold text-chart-2 mt-1" aria-label={`${highestFocusDay.thisWeek} minutes on ${highestFocusDay.name}`}>
            {highestFocusDay.thisWeek} min
          </p>
          <div className="flex items-center mt-1">
            <span className="text-xs text-muted-foreground" aria-label="Best performance this week">
              Best performance
            </span>
          </div>
        </div>
      </div>

      {/* Bar chart */}
      <div 
        className="h-[220px] sm:h-[250px] w-full" 
        role="region" 
        aria-label="Weekly comparison chart showing focus time for this week and last week"
      >
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={data} margin={{ top: 20, right: 5, left: -15, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" vertical={false} />
            <XAxis
              dataKey="name"
              stroke="currentColor"
              className="text-muted-foreground"
              tickLine={false}
              axisLine={false}
              tick={{ fontSize: 12 }}
              aria-label="Days of the week"
            />
            <YAxis
              stroke="currentColor"
              className="text-muted-foreground"
              tickLine={false}
              axisLine={false}
              tick={{ fontSize: 12 }}
              width={30}
              label={{ value: 'minutes', position: 'top', offset: 15, style: { fontSize: 11, fill: 'currentColor', textAnchor: 'middle' } }}
              aria-label="Focus minutes"
            />
            <Tooltip content={<CustomTooltip />} />
            <Legend
              wrapperStyle={{ paddingTop: 10 }}
              formatter={(value) => <span className="text-xs text-muted-foreground">{value}</span>}
            />
            <Bar
              dataKey="thisWeek"
              name="This Week"
              fill="#5576F5"
              radius={[4, 4, 0, 0]}
              isAnimationActive={true}
              animationDuration={1000}
              animationEasing="ease-out"
            >
              <LabelList content={renderCustomLabel} />
            </Bar>
            <Bar 
              dataKey="lastWeek" 
              name="Last Week" 
              fill="#36A3F2"
              radius={[4, 4, 0, 0]}
              isAnimationActive={true}
              animationDuration={1000}
              animationEasing="ease-out"
              animationBegin={300}
            >
              <LabelList content={renderCustomLabel} />
            </Bar>
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  )
}
