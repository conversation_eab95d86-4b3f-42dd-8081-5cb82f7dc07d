# YouTube Background Playback Implementation

## Overview

This document describes the implementation of YouTube background playback functionality that addresses two key issues:
1. **Mobile Sheet Closing**: YouTube audio continues when the mobile music control sheet is closed
2. **Browser Tab Switching**: YouTube audio attempts to resume when switching back to the app tab

## Architecture

### Components

1. **MobileYouTubeManager** (`src/components/audio/mobile-youtube-manager.tsx`)
   - Persistent YouTube player instance for mobile devices
   - Always mounted, independent of UI visibility
   - Handles tab visibility changes and resume attempts

2. **MobileYouTubePlayer** (`src/app/timer/_components/musics-control/mobile-youtube-player.tsx`)
   - Mobile-specific UI component
   - Connects to MobileYouTubeManager via global API
   - Shows user notifications about tab switching behavior

3. **Enhanced Desktop Player** (`src/app/timer/_components/musics-control/use-youtube-player.ts`)
   - Added tab visibility handling to desktop implementation
   - Same resume logic as mobile version

4. **Device Detection** (`src/lib/device-utils.ts`)
   - Consistent mobile device detection
   - Used to determine which player implementation to use

## Tab Switching Behavior

### The Challenge

YouTube's iframe API automatically pauses video playback when the browser tab becomes inactive (`document.hidden = true`). This is intentional behavior by YouTube to:
- Prevent background audio abuse
- Comply with browser policies
- Save system resources

### Our Solution

Since we cannot completely override YouTube's behavior, we implement a **resume-on-return** strategy:

1. **Detection**: Monitor `visibilitychange` events
2. **State Tracking**: Remember if video was playing before tab became hidden
3. **Resume Attempts**: When tab becomes visible again, attempt to resume playback
4. **Retry Logic**: Multiple attempts with delays to handle timing issues
5. **User Notification**: Inform users about the limitation

### Implementation Details

```javascript
// Tab visibility change handler
const handleVisibilityChange = () => {
  if (document.hidden) {
    // Store playing state before tab becomes hidden
    wasPlayingBeforeHiddenRef.current = isCurrentlyPlaying;
  } else {
    // Attempt to resume if was playing before
    if (wasPlayingBeforeHiddenRef.current) {
      attemptResumeWithRetries();
    }
  }
};
```

### Resume Strategy

1. **Immediate Attempt**: Try to resume after 100ms delay
2. **Verification**: Check if playback actually started after 500ms
3. **Retry Logic**: Up to 3 attempts with 1-second intervals
4. **Fallback**: User can manually resume if automatic attempts fail

## YouTube Player Optimizations

### Enhanced Player Parameters

```javascript
playerVars: {
  // Standard parameters
  autoplay: 0,
  controls: 1,
  modestbranding: 1,
  rel: 0,
  showinfo: 0,
  iv_load_policy: 3,
  disablekb: 0,
  fs: 1,
  cc_load_policy: 0,
  origin: window.location.origin,
  enablejsapi: 1,
  
  // Background playback optimizations
  playsinline: 1, // Helps with mobile background playback
  widget_referrer: window.location.origin,
}
```

### Key Optimizations

- **playsinline**: Prevents fullscreen on mobile, helps with background playback
- **widget_referrer**: Provides proper referrer context
- **origin**: Ensures proper iframe communication

## User Experience

### Mobile Experience

1. **Sheet Independence**: Audio continues when music control sheet is closed
2. **Background Status**: UI shows "Playing in background" status
3. **Tab Switch Notification**: Informative tip about tab switching behavior
4. **Auto-Resume**: Attempts to resume playback when returning to tab

### Desktop Experience

1. **Consistent Behavior**: Same tab switching resume logic as mobile
2. **Existing UI**: No changes to desktop interface
3. **Background Playback**: Works within browser tab limitations

## Testing Guide

### Test Scenarios

#### 1. Mobile Sheet Closing
- ✅ Open music control sheet on mobile
- ✅ Load and play YouTube video
- ✅ Close sheet → Audio should continue
- ✅ Reopen sheet → Controls should show correct state

#### 2. Tab Switching - Mobile
- ✅ Play YouTube video on mobile
- ✅ Switch to different browser tab
- ✅ Wait 5-10 seconds
- ✅ Return to app tab → Audio should attempt to resume

#### 3. Tab Switching - Desktop
- ✅ Play YouTube video on desktop
- ✅ Switch to different browser tab
- ✅ Wait 5-10 seconds
- ✅ Return to app tab → Audio should attempt to resume

#### 4. State Synchronization
- ✅ Start/stop playback multiple times
- ✅ Adjust volume and mute settings
- ✅ Switch tabs and return
- ✅ Verify all controls remain synchronized

### Browser Testing

Test across different browsers to understand behavior variations:

- **Chrome**: Primary target, most restrictive tab policies
- **Firefox**: May have different tab throttling behavior
- **Safari**: iOS Safari has unique background audio policies
- **Edge**: Similar to Chrome but may have slight differences

### Expected Behaviors

#### What Works
- ✅ Audio continues when mobile sheet closes
- ✅ Automatic resume attempts when returning to tab
- ✅ State synchronization between UI and player
- ✅ User notifications about limitations

#### Known Limitations
- ⚠️ YouTube may still pause on tab switch (browser/YouTube policy)
- ⚠️ Resume attempts may not always succeed immediately
- ⚠️ Some browsers may be more restrictive than others
- ⚠️ Very long tab switches may require manual resume

## Troubleshooting

### Common Issues

1. **Resume Not Working**
   - Check browser console for error messages
   - Verify YouTube iframe API is loaded
   - Try manual play button after tab switch

2. **Mobile Detection Issues**
   - Verify device detection logic
   - Check if correct player component is being used
   - Test on actual mobile devices, not just browser emulation

3. **State Synchronization Problems**
   - Check global API exposure (`window.mobileYouTubeManager`)
   - Verify subscription/unsubscription logic
   - Look for React strict mode double-mounting issues

### Debug Logging

The implementation includes comprehensive console logging:
- Tab visibility changes
- Resume attempts and results
- Player state changes
- Error conditions

Enable browser console to see detailed debug information.

## Future Improvements

### Potential Enhancements

1. **Audio Context API**: Explore using Web Audio API for more control
2. **Service Worker**: Investigate service worker-based background audio
3. **Progressive Web App**: PWA features might provide better background capabilities
4. **Alternative Sources**: Fallback to audio-only sources when available
5. **User Preferences**: Allow users to configure resume behavior

### Browser API Evolution

Monitor browser API changes that might provide better background audio support:
- Background Sync API
- Media Session API enhancements
- New iframe sandbox permissions
- YouTube API updates

## Conclusion

This implementation provides the best possible YouTube background playback experience within current browser and YouTube API limitations. While we cannot completely prevent YouTube from pausing on tab switches, we provide automatic resume functionality and clear user communication about the behavior.

The solution successfully addresses the primary requirement of maintaining audio when the mobile music control sheet is closed, while also improving the tab switching experience as much as technically possible.
