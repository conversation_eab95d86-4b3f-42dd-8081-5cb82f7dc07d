'use client';

import React from 'react';
import { <PERSON>, <PERSON>, <PERSON>, Infinity } from 'lucide-react';
import { motion } from 'framer-motion';

interface TimerCycleVisualizationProps {
  pomodoroMinutes: number;
  shortBreakMinutes: number;
  longBreakMinutes: number;
  sessionsCount: number;
  timerMode?: 'countDown' | 'countUp';
}

export const TimerCycleVisualization = React.memo(({
  pomodoroMinutes,
  shortBreakMinutes,
  longBreakMinutes,
  sessionsCount,
  timerMode = 'countDown'
}: TimerCycleVisualizationProps) => {
  const MotionDiv = motion.div;

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.05
      }
    }
  };

  const item = {
    hidden: { scale: 0.9, opacity: 0 },
    show: { scale: 1, opacity: 1 }
  };

  return (
    <MotionDiv
      className="flex flex-wrap items-center gap-1 overflow-x-auto overflow-y-auto scrollbar-hide py-1 pb-2"
      variants={container}
      initial="hidden"
      animate="show"
      style={{ maxWidth: '100%' }}
    >
      {Array.from({ length: sessionsCount }).map((_, i) => (
        <React.Fragment key={`cycle-${i}`}>
          {i > 0 && (
            <MotionDiv
              className="text-gray-400 dark:text-gray-600 text-sm font-medium mx-0.5"
              variants={item}
            >
              →
            </MotionDiv>
          )}

          <MotionDiv
            className="px-1.5 py-0.5 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-md text-xs flex items-center gap-0.5 shadow-sm"
            variants={item}
          >
            {timerMode === 'countUp' ? (
              <>
                <Infinity className="h-2.5 w-2.5" />
                <span className="text-xs">Focus</span>
              </>
            ) : (
              <>
                <Clock className="h-2.5 w-2.5" />
                <span className="text-xs">{pomodoroMinutes}m</span>
              </>
            )}
          </MotionDiv>

          {i < sessionsCount - 1 && (
            <>
              <MotionDiv
                className="text-gray-400 dark:text-gray-600 text-sm font-medium mx-0.5"
                variants={item}
              >
                →
              </MotionDiv>

              <MotionDiv
                className="px-1.5 py-0.5 bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-300 rounded-md text-xs flex items-center gap-0.5 shadow-sm"
                variants={item}
              >
                <Coffee className="h-2.5 w-2.5" />
                <span className="text-xs">{shortBreakMinutes}m</span>
              </MotionDiv>
            </>
          )}
        </React.Fragment>
      ))}

      <MotionDiv
        className="text-gray-400 dark:text-gray-600 text-sm font-medium mx-0.5"
        variants={item}
      >
        →
      </MotionDiv>

      <MotionDiv
        className="px-1.5 py-0.5 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 rounded-md text-xs flex items-center gap-0.5 shadow-sm"
        variants={item}
      >
        <Moon className="h-2.5 w-2.5" />
        <span className="text-xs">{longBreakMinutes}m</span>
      </MotionDiv>
    </MotionDiv>
  );
});

TimerCycleVisualization.displayName = 'TimerCycleVisualization';