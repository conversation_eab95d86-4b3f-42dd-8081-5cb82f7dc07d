"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  ClipboardList,
  Sparkles,
  BarChart3,
  Target,
  TrendingUp,
  AlertCircle,
  Activity,
  Brain,
  Zap,
  Clock
} from "lucide-react"

interface TasksContentProps {
  activeTab: string
}

export function TasksContent({ }: TasksContentProps) {
  const features = [
    {
      icon: BarChart3,
      title: "Real-time Statistics",
      description: "Live task metrics",
      color: "text-blue-500",
      bgColor: "bg-blue-500/10"
    },
    {
      icon: Target,
      title: "Productivity Metrics",
      description: "Performance tracking",
      color: "text-emerald-500",
      bgColor: "bg-emerald-500/10"
    },
    {
      icon: TrendingUp,
      title: "Completion Trends",
      description: "Progress analysis",
      color: "text-purple-500",
      bgColor: "bg-purple-500/10"
    },
    {
      icon: AlertCircle,
      title: "Priority Management",
      description: "Task prioritization",
      color: "text-amber-500",
      bgColor: "bg-amber-500/10"
    },
    {
      icon: Activity,
      title: "Progress Tracking",
      description: "Activity monitoring",
      color: "text-rose-500",
      bgColor: "bg-rose-500/10"
    },
    {
      icon: Brain,
      title: "Performance Insights",
      description: "Smart analytics",
      color: "text-indigo-500",
      bgColor: "bg-indigo-500/10"
    }
  ]

  return (
    <div className="w-full max-w-6xl mx-auto p-4 space-y-6 opacity-0 animate-[fadeIn_0.5s_ease-out_forwards]">
      {/* Header Section */}
      <div className="text-center space-y-3">
        <div className="relative inline-flex items-center justify-center">
          <div className="relative p-3 rounded-2xl bg-gradient-to-br from-orange-500/20 via-red-500/20 to-rose-600/20 border border-orange-400/30 shadow-lg">
            <ClipboardList className="h-8 w-8 text-orange-500" />
            <div className="absolute -top-1 -right-1">
              <Sparkles className="h-4 w-4 text-yellow-400 animate-pulse" />
            </div>
          </div>
        </div>

        <div className="space-y-1">
          <h1 className="text-2xl font-bold text-foreground">Task Management Hub</h1>
          <p className="text-sm text-muted-foreground max-w-md mx-auto">
            Advanced productivity tools and insights coming soon
          </p>
        </div>

        <Badge
          variant="outline"
          className="px-3 py-1 text-xs font-medium bg-gradient-to-r from-orange-500/10 via-red-500/10 to-rose-600/10 border-orange-400/30 text-orange-600 dark:text-orange-400"
        >
          <Zap className="h-3 w-3 mr-1" />
          Coming Soon
        </Badge>
      </div>

      {/* Feature Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
        {features.map((feature, index) => {
          const IconComponent = feature.icon
          return (
            <Card
              key={index}
              className="group bg-neutral-300/20 hover:bg-neutral-300/30 dark:bg-neutral-400/20 dark:hover:bg-neutral-400/30 backdrop-blur-[1px] border border-neutral-400/20 transition-all duration-300 hover:scale-[1.02] hover:shadow-lg"
            >
              <CardContent className="p-4">
                <div className="flex items-start space-x-3">
                  <div className={`p-2 rounded-lg ${feature.bgColor} transition-transform duration-300 group-hover:scale-110`}>
                    <IconComponent className={`h-4 w-4 ${feature.color}`} />
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="text-sm font-semibold text-foreground truncate">
                      {feature.title}
                    </h3>
                    <p className="text-xs text-muted-foreground mt-0.5">
                      {feature.description}
                    </p>
                  </div>
                  <div className="flex-shrink-0">
                    <Clock className="h-3 w-3 text-muted-foreground/50 animate-spin" style={{ animationDuration: '4s' }} />
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Bottom Message */}
      <div className="text-center">
        <p className="text-xs text-muted-foreground/70">
          Building powerful task management tools for enhanced productivity
        </p>
      </div>

      <style jsx>{`
        @keyframes fadeIn {
          from {
            opacity: 0;
            transform: translateY(10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      `}</style>
    </div>
  )
}
