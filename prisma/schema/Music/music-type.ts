import { z } from "zod";
import { MusicSchema, MusicPartialSchema } from "@types";
import { MusicGenreSchema, MediaSourceSchema } from "@types";

export const createMusicSchema = MusicSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  userId: true,
  creatorType: true,
}).extend({
  isPublic: z.union([
    z.boolean(),
    z.string().transform(val => val === "true")
  ]).optional().default(false),
  isCopyright: z.union([
    z.boolean(),
    z.string().transform(val => val === "true")
  ]).optional().default(false),
  useAsOpeningMusic: z.union([
    z.boolean(),
    z.string().transform(val => val === "true")
  ]).optional().default(false),
  src: z
    .string()
    .transform((value) => {
      // Handle empty strings, "undefined" strings, and null/undefined values
      if (!value || value === "" || value === "undefined" || value === "null") {
        return undefined;
      }
      return value;
    })
    .optional()
    .refine((value) => {
      // Only validate as URL if value exists and is not empty
      if (!value) return true;
      try {
        new URL(value);
        return true;
      } catch {
        return false;
      }
    }, {
      message: "Please enter a valid URL"
    }),
  source: MediaSourceSchema.optional(),
  rating: z
    .union([
      z.number().min(0).max(5),
      z.string().transform((val) => {
        const num = parseFloat(val);
        return isNaN(num) ? undefined : num;
      })
    ])
    .optional(),
  duration: z
    .union([
      z.number().min(0),
      z.string().transform((val) => {
        const num = parseInt(val);
        return isNaN(num) ? undefined : num;
      })
    ])
    .optional(),
  note: z
    .string()
    .transform((value) => {
      // Handle empty strings
      if (!value || value.trim() === "") {
        return undefined;
      }
      return value.trim();
    })
    .optional(),
  genres: MusicGenreSchema["array"]().optional(),
  playlistId: z.string().optional().nullable(),
});

export const updateMusicSchema = MusicPartialSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  userId: true,
  creatorType: true,
}).extend({
  isPublic: z.union([
    z.boolean(),
    z.string().transform(val => val === "true")
  ]).optional(),
  isCopyright: z.union([
    z.boolean(),
    z.string().transform(val => val === "true")
  ]).optional(),
  useAsOpeningMusic: z.union([
    z.boolean(),
    z.string().transform(val => val === "true")
  ]).optional(),
  src: z
    .string()
    .transform((value) => {
      // Handle empty strings, "undefined" strings, and null/undefined values
      if (!value || value === "" || value === "undefined" || value === "null") {
        return undefined;
      }
      return value;
    })
    .optional()
    .refine((value) => {
      // Only validate as URL if value exists and is not empty
      if (!value) return true;
      try {
        new URL(value);
        return true;
      } catch {
        return false;
      }
    }, {
      message: "Please enter a valid URL"
    }),
  source: MediaSourceSchema.optional(),
  rating: z
    .union([
      z.number().min(0).max(5),
      z.string().transform((val) => {
        const num = parseFloat(val);
        return isNaN(num) ? undefined : num;
      })
    ])
    .optional(),
  duration: z
    .union([
      z.number().min(0),
      z.string().transform((val) => {
        const num = parseInt(val);
        return isNaN(num) ? undefined : num;
      })
    ])
    .optional(),
  note: z
    .string()
    .transform((value) => {
      // Handle empty strings
      if (!value || value.trim() === "") {
        return undefined;
      }
      return value.trim();
    })
    .optional(),
  genres: MusicGenreSchema["array"]().optional(),
  playlistId: z.string().optional().nullable(),
});

export type CreateMusicInput = z.infer<typeof createMusicSchema>;
export type UpdateMusicInput = z.infer<typeof updateMusicSchema>;