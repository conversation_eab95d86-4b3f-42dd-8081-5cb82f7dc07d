'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { systemPlayerManager } from './system-player-manager';
import { MusicControlProps } from './types';
import { useGlobalMusicControl } from '@/lib/use-global-music-control';
import { usePomodoroStore } from '@/lib/pomodoro-store';

/**
 * Hook that integrates SystemPlayerManager with the global music control system.
 * This replaces the old useSystemPlayerManager hook with global coordination.
 */
export function useGlobalSystemPlayer(playlist: MusicControlProps['playlist']) {
  // Use consistent player ID based on playlist to prevent conflicts between MobileAudioManager and SystemPlayer
  // This ensures both instances are treated as the same audio source by the global control system
  const playlistId = playlist?.id || 'no-playlist';
  const playerIdRef = useRef(`global-system-player-${playlistId}`);
  const playerId = playerIdRef.current;

  // Track initialization to prevent multiple calls
  const isInitializedRef = useRef(false);
  const currentPlaylistIdRef = useRef<string | null>(null);

  // State from the global manager
  const [state, setState] = useState(() => systemPlayerManager.getState());

  // Get current selected video for debugging
  const selectedVideo = usePomodoroStore(state => state.selectedVideo);

  // Global music control integration
  const globalMusicControl = useGlobalMusicControl({
    sourceId: playerId,
    sourceType: 'system',
    sourceName: `System Player (${playlist?.name || 'Unknown'})`,
    persistOnUnmount: true, // Prevent audio from stopping when UI is hidden
    onPlayStart: () => {
      console.log(`Global System Player ${playerId}: Play start requested by global control`);
    },
    onPause: () => {
      console.log(`Global System Player ${playerId}: Pause requested by global control - pausing music only, keeping nature sounds`);
      // Only pause if we're not just transferring control between instances of the same content
      const currentState = systemPlayerManager.getState();
      if (currentState.isMainPlaying) {
        systemPlayerManager.pauseMainOnly(); // Only pause music, keep nature sounds playing
      }
    },
  });

  // Store globalMusicControl in ref to avoid dependency issues
  const globalMusicControlRef = useRef(globalMusicControl);
  globalMusicControlRef.current = globalMusicControl;

  // Update state when the global manager changes
  useEffect(() => {
    const handleStateChange = (event: CustomEvent) => {
      const newState = event.detail;
      setState(newState);

      // Only update global music control if this player has control
      // and avoid circular updates by checking if we're the active source
      const control = globalMusicControlRef.current;
      if (control.hasControl && control.isActive) {
        // Debounce global state updates to prevent excessive calls
        const updateGlobalState = () => {
          control.updateState({
            isPlaying: newState.isMainPlaying,
            volume: newState.mainVolume,
            isMuted: newState.isMainMuted,
            currentTrack: newState.currentTrack ? {
              id: newState.currentTrack.id,
              title: newState.currentTrack.title,
              src: newState.currentTrack.src || '',
              type: 'music',
            } : null,
            currentTime: newState.currentTime,
            duration: newState.duration,
          });
        };

        // Use requestAnimationFrame to debounce updates
        requestAnimationFrame(updateGlobalState);
      }
    };

    window.addEventListener('system-player-state-change', handleStateChange as EventListener);

    return () => {
      window.removeEventListener('system-player-state-change', handleStateChange as EventListener);
    };
  }, []); // Remove globalMusicControl from dependencies to prevent circular updates

  // Initialize the global manager when playlist changes
  useEffect(() => {
    console.log(`Global System Player ${playerId}: useEffect triggered - playlist:`, playlist?.name, `(ID: ${playlist?.id})`);

    if (!playlist) {
      console.log(`Global System Player ${playerId}: No playlist provided, cleaning up`);
      isInitializedRef.current = false;
      currentPlaylistIdRef.current = null;
      return;
    }

    const playlistId = playlist.id;

    // Always re-initialize when playlist changes to ensure proper video-to-playlist synchronization
    // This is especially important for mobile swipe gestures where videos change frequently
    console.log(`Global System Player ${playerId}: Initializing with playlist`, playlist.name, `(ID: ${playlistId})`);
    console.log(`Global System Player ${playerId}: Selected video:`, selectedVideo?.title, `(Video ID: ${selectedVideo?.id})`);
    console.log(`Global System Player ${playerId}: Playlist music count:`, 'musics' in playlist ? playlist.musics?.length || 0 : 0, `nature sounds count:`, 'natureSounds' in playlist ? playlist.natureSounds?.length || 0 : 0);

    // Check if SystemPlayerManager is already initialized with the same playlist and currently playing
    const currentState = systemPlayerManager.getState();
    const isAlreadyInitialized = currentState.isInitialized &&
                                currentState.playlist?.id === playlistId;
    const shouldPreservePlayback = isAlreadyInitialized && currentState.isMainPlaying;
    const isPlayingSameContent = systemPlayerManager.isPlayingSameContent(playlist, 0);

    console.log(`Global System Player ${playerId}: Already initialized:`, isAlreadyInitialized, 'Should preserve playback:', shouldPreservePlayback, 'Playing same content:', isPlayingSameContent);

    // Request control from global music system
    const control = globalMusicControlRef.current;
    const hasControl = control.requestControl();

    if (hasControl) {
      // Only auto-play if this is the first initialization or if we're not preserving playback
      // Also check if we're already playing the same content to avoid restarts
      const shouldAutoPlay = !shouldPreservePlayback && !isPlayingSameContent;
      console.log(`Global System Player ${playerId}: Initializing with auto-play:`, shouldAutoPlay);

      // Initialize the global manager with this playlist
      systemPlayerManager.initialize(playlist, shouldAutoPlay);
    } else {
      // Initialize without auto-play if we don't have control
      systemPlayerManager.initialize(playlist, false);
    }

    // Mark as initialized
    isInitializedRef.current = true;
    currentPlaylistIdRef.current = playlistId;

    return () => {
      console.log(`Global System Player ${playerId}: Cleaning up`);
      globalMusicControlRef.current.releaseControl();
      isInitializedRef.current = false;
      currentPlaylistIdRef.current = null;
    };
  }, [playlist, playerId, selectedVideo?.id, selectedVideo?.title]); // Include full playlist dependency

  // Control functions that work with global music control
  const playMainTrack = useCallback((index: number) => {
    const control = globalMusicControlRef.current;
    const canPlay = control.requestControl();
    if (canPlay) {
      systemPlayerManager.playMainTrack(index);

      // Notify global control of play start (debounced)
      setTimeout(() => {
        const currentTrack = systemPlayerManager.getState().currentTrack;
        if (currentTrack) {
          control.notifyPlayStart({
            id: currentTrack.id,
            title: currentTrack.title,
            src: currentTrack.src || '',
            type: 'music',
          });
        }
      }, 0);
    }
  }, []);

  const toggleMainPlay = useCallback(() => {
    const control = globalMusicControlRef.current;
    const currentState = systemPlayerManager.getState();

    if (currentState.isMainPlaying) {
      // Pausing - no need to request control
      systemPlayerManager.toggleMainPlay();
      setTimeout(() => control.notifyPause(), 0);
    } else {
      // Playing - request control first
      const canPlay = control.requestControl();
      if (canPlay) {
        systemPlayerManager.toggleMainPlay();

        // Notify global control of play start (debounced)
        setTimeout(() => {
          const currentTrack = currentState.currentTrack;
          if (currentTrack) {
            control.notifyPlayStart({
              id: currentTrack.id,
              title: currentTrack.title,
              src: currentTrack.src || '',
              type: 'music',
            });
          }
        }, 0);
      }
    }
  }, []);

  const skipToNext = useCallback(() => {
    const control = globalMusicControlRef.current;
    const canPlay = control.requestControl();
    if (canPlay) {
      systemPlayerManager.skipToNext();
    }
  }, []);

  const skipToPrevious = useCallback(() => {
    const control = globalMusicControlRef.current;
    const canPlay = control.requestControl();
    if (canPlay) {
      systemPlayerManager.skipToPrevious();
    }
  }, []);

  const toggleMainMute = useCallback(() => {
    systemPlayerManager.toggleMainMute();

    // Update global state (debounced)
    setTimeout(() => {
      const newState = systemPlayerManager.getState();
      globalMusicControlRef.current.updateState({
        isMuted: newState.isMainMuted,
      });
    }, 0);
  }, []);

  const handleMainVolumeChange = useCallback((value: number[]) => {
    const volume = value[0];
    systemPlayerManager.setMainVolume(volume);

    // Update global state (debounced)
    setTimeout(() => {
      globalMusicControlRef.current.updateState({
        volume,
        isMuted: volume === 0,
      });
    }, 0);
  }, []);

  const togglePlaybackMode = useCallback(() => {
    systemPlayerManager.togglePlaybackMode();
  }, []);

  const handleSeek = useCallback((time: number) => {
    systemPlayerManager.handleSeek(time);

    // Update global state (debounced)
    setTimeout(() => {
      globalMusicControlRef.current.updateState({
        currentTime: time,
      });
    }, 0);
  }, []);

  const toggleNatureSound = useCallback((soundId: string) => {
    systemPlayerManager.toggleNatureSound(soundId);
  }, []);

  const handleNatureSoundVolume = useCallback((soundId: string, value: number[]) => {
    const volume = value[0];
    systemPlayerManager.handleNatureSoundVolume(soundId, volume);
  }, []);

  const toggleNatureSoundMute = useCallback((soundId: string) => {
    systemPlayerManager.toggleNatureSoundMute(soundId);
  }, []);

  const pauseMainOnly = useCallback(() => {
    systemPlayerManager.pauseMainOnly();
    setTimeout(() => globalMusicControlRef.current.notifyPause(), 0);
  }, []);

  const pauseAll = useCallback(() => {
    systemPlayerManager.pauseAll();
    setTimeout(() => globalMusicControlRef.current.notifyPause(), 0);
  }, []);

  // Return state and control functions
  return {
    // State
    currentTrack: state.currentTrack,
    currentMusicIndex: state.currentMusicIndex,
    isMainPlaying: state.isMainPlaying,
    mainVolume: state.mainVolume,
    isMainMuted: state.isMainMuted,
    playbackMode: state.playbackMode,
    currentTime: state.currentTime,
    duration: state.duration,
    natureSounds: state.natureSounds,
    playlist: state.playlist,
    isInitialized: state.isInitialized,
    musicCount: state.musicCount,
    playingNatureSoundsCount: systemPlayerManager.getPlayingNatureSoundsCount(),
    
    // Control functions
    playMainTrack,
    toggleMainPlay,
    skipToNext,
    skipToPrevious,
    toggleMainMute,
    handleMainVolumeChange,
    togglePlaybackMode,
    handleSeek,
    toggleNatureSound,
    handleNatureSoundVolume,
    toggleNatureSoundMute,
    pauseMainOnly,
    pauseAll,
    
    // Global control state
    hasGlobalControl: globalMusicControl.hasControl,
    isGloballyActive: globalMusicControl.isActive,
    canPlay: globalMusicControl.canPlay,
    playerId,
  };
}
