import { PlaylistTypeResponse } from '@schemas/Video/video-query';

export interface MusicControlProps {
  playlist: {
    id: string;
    name: string;
    musics?: {
      id: string;
      title: string;
      src?: string | null;
      createdAt?: string | Date;
      updatedAt?: string | Date;
      isPublic?: boolean;
      creatorType?: string;
      userId?: string;
      genres?: string[];
      duration?: number | null;
      note?: string | null;
    }[];
    natureSounds?: {
      id: string;
      title: string;
      src?: string | null;
      category?: string[];
      createdAt?: string | Date;
      updatedAt?: string | Date;
      isPublic?: boolean;
      creatorType?: string;
      userId?: string;
    }[];
    description?: string;
    imageUrl?: string | null;
    isPublic?: boolean;
    isDefault?: boolean;
    creatorType?: string;
    userId?: string;
    musicOrder?: string[];
    // Make these optional since they may not be present in all playlist sources
    createdAt?: string | Date;
    updatedAt?: string | Date;
    genres?: string[];
  } | PlaylistTypeResponse | null;
  className?: string;
  variant?: 'popover' | 'sheet'; // New prop to control styling variant
}

export type NatureSoundPlayer = {
  id: string;
  title: string;
  audioElement: HTMLAudioElement | null;
  isPlaying: boolean;
  volume: number;
  isMuted: boolean;
};

// System player compatible nature sound type (without audioElement)
export type SystemNatureSoundPlayer = {
  id: string;
  title: string;
  src: string;
  category: string[];
  isPlaying: boolean;
  volume: number;
  isMuted: boolean;
};

export enum PlaybackMode {
  LOOP_ALL = "LOOP_ALL",
  LOOP_ONE = "LOOP_ONE",
  SHUFFLE = "SHUFFLE"
}

export type TabType = 'system' | 'social' | 'spotify';

export interface YouTubePlayerState {
  currentVideoId: string;
  isPlaying: boolean;
  volume: number;
  isMuted: boolean;
  inputUrl: string;
}

export interface SoundCloudPlayerState {
  currentTrackUrl: string;
  embedUrl: string;
  inputUrl: string;
  isValid: boolean;
  trackType: 'track' | 'album' | 'playlist' | 'episode' | 'show' | null;
}

export interface SoundCloudUrlParts {
  type: 'track' | 'album' | 'playlist' | 'episode' | 'show';
  id: string;
  isValid: boolean;
} 