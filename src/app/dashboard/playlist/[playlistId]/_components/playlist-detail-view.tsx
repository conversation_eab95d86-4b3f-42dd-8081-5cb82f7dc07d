"use client"

import { useState, useMemo } from "react"
import { useGetMusicPlaylistUser } from "@schemas/MusicPlaylist/music-playlist-user-query"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Plus, Music2, Clock, Calendar, Play, Pause, ArrowLeft, Waves } from "lucide-react"
import Image from "next/image"
import { motion, AnimatePresence } from "framer-motion"

import { AddMusicDialog } from "./add-music-dialog"
import { AddNatureSoundsDialog } from "./add-natural-sounds-dialog"
import { MusicTrackItem } from "./music-track-item"
import { NatureSoundItem } from "./natural-sound-item"
import { useRouter } from "next/navigation"
import { toast } from "sonner"
import { useAudioStore } from "@/lib/audio-store"

interface PlaylistDetailViewProps {
  playlistId: string
}

export function PlaylistDetailView({ playlistId }: PlaylistDetailViewProps) {
  const router = useRouter()
  const [isAddMusicDialogOpen, setIsAddMusicDialogOpen] = useState(false)
  const [isAddNatureSoundsDialogOpen, setIsAddNatureSoundsDialogOpen] = useState(false)
  const [activeTab, setActiveTab] = useState("music")

  const { data: playlist, isLoading: isLoadingPlaylist, error: playlistError } = useGetMusicPlaylistUser(playlistId)

  // Global audio store for bottom player
  const {
    globalPlayer,
    setGlobalPlayerTrack,
    setGlobalPlayerPlaying,
    stopGlobalPlayer,
    setGlobalPlayerPlaylist,
    playAllTracks,
    skipToNextTrack,
    setPlaylistMode,
    stopNatureSounds,
  } = useAudioStore()

  // We no longer need the local audio player hook since we're using the global player

  // Get musics and natural sounds from the playlist data
  const musics = useMemo(() => playlist?.musics || [], [playlist?.musics])
  const natureSounds = useMemo(() => playlist?.natureSounds || [], [playlist?.natureSounds])
  const isLoadingMusics = isLoadingPlaylist
  const isLoadingNatureSounds = isLoadingPlaylist

  // Convert playlist data to track format for global player
  const allTracks = useMemo(() => {
    const musicTracks = musics.map(music => ({
      id: music.id,
      title: music.title,
      src: music.src,
      type: 'music' as const,
      genres: music.genres,
      duration: music.duration,
    }))

    const natureSoundTracks = natureSounds.map(sound => ({
      id: sound.id,
      title: sound.title,
      src: sound.src,
      type: 'nature-sound' as const,
      category: sound.category,
    }))

    return [...musicTracks, ...natureSoundTracks]
  }, [musics, natureSounds])

  const handlePlayPause = () => {
    if (allTracks.length === 0) {
      toast.error("No tracks available to play")
      return
    }

    if (!globalPlayer.isPlaylistMode) {
      // Always start "Play All" mode from the beginning, regardless of current track
      const playlistTracks = allTracks.map(track => ({
        id: track.id,
        title: track.title,
        src: track.src || '',
        type: track.type,
        genres: track.type === 'music' ? track.genres : undefined,
        category: track.type === 'nature-sound' ? track.category : undefined,
      }))

      setGlobalPlayerPlaylist(playlistTracks)
      playAllTracks()
    } else {
      // Toggle play/pause on global player when already in playlist mode
      setGlobalPlayerPlaying(!globalPlayer.isPlaying)
    }
  }

  const handleTrackPlay = (trackId: string) => {
    const track = allTracks.find(t => t.id === trackId)
    if (track && track.src) {
      // Check if this track is already playing - if so, toggle play/pause
      if (globalPlayer.currentTrack?.id === trackId) {
        setGlobalPlayerPlaying(!globalPlayer.isPlaying)
      } else {
        // Exit playlist mode and stop nature sounds when playing individual track
        setPlaylistMode(false)
        stopNatureSounds()

        // Set new track to play
        setGlobalPlayerTrack({
          id: track.id,
          title: track.title,
          src: track.src,
          type: track.type,
          genres: track.type === 'music' ? track.genres : undefined,
          category: track.type === 'nature-sound' ? track.category : undefined,
        })
      }
    }
  }

  const handleNatureSoundPlay = (soundId: string) => {
    const sound = allTracks.find(t => t.id === soundId)
    if (sound && sound.src) {
      // Check if this sound is already playing - if so, toggle play/pause
      if (globalPlayer.currentTrack?.id === soundId) {
        setGlobalPlayerPlaying(!globalPlayer.isPlaying)
      } else {
        // Exit playlist mode and stop nature sounds when playing individual sound
        setPlaylistMode(false)
        stopNatureSounds()

        // Set new sound to play
        setGlobalPlayerTrack({
          id: sound.id,
          title: sound.title,
          src: sound.src,
          type: sound.type,
          genres: sound.type === 'music' ? sound.genres : undefined,
          category: sound.type === 'nature-sound' ? sound.category : undefined,
        })
      }
    }
  }



  if (playlistError) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
        <div className="text-center space-y-2">
          <h2 className="text-2xl font-semibold text-muted-foreground">Playlist Not Found</h2>
          <p className="text-muted-foreground">The playlist you're looking for doesn't exist or you don't have permission to view it.</p>
        </div>
        <Button variant="outline" onClick={() => router.back()}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Go Back
        </Button>
      </div>
    )
  }

  if (isLoadingPlaylist) {
    return <PlaylistDetailSkeleton />
  }

  if (!playlist) {
    return null
  }

  const totalTracks = (playlist.musics?.length || 0) + (playlist.natureSounds?.length || 0) + (playlist.videos?.length || 0)
  const estimatedDuration = Math.floor(totalTracks * 3.5) // Estimated duration in minutes
  const creationDate = new Date(playlist.createdAt).toLocaleDateString(undefined, {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  })

  return (
    <div
      className="space-y-6"
      style={{
        paddingBottom: 'var(--global-audio-player-height, 0px)'
      }}
    >
      {/* Header Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card className="relative overflow-hidden border-muted/50 bg-gradient-to-br from-orange-50/50 via-white to-rose-50/50 dark:from-orange-950/20 dark:via-background dark:to-rose-950/20">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-6 items-start">
              {/* Playlist Artwork */}
              <div className="relative w-48 h-48 md:w-40 md:h-40 lg:w-48 lg:h-48 rounded-xl overflow-hidden shrink-0 group shadow-lg">
                {playlist.imageUrl ? (
                  <Image
                    src={playlist.imageUrl}
                    alt={playlist.name}
                    fill
                    className="object-cover transition-transform duration-500 group-hover:scale-110"
                    sizes="(max-width: 768px) 192px, (max-width: 1024px) 160px, 192px"
                  />
                ) : (
                  <div className="w-full h-full bg-gradient-to-br from-orange-200 via-red-100 to-rose-200 dark:from-orange-900/60 dark:via-red-900/40 dark:to-rose-900/60 flex items-center justify-center relative overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-r from-orange-400/30 to-rose-400/30" />
                    <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_30%,_rgba(251,_146,_60,_0.3),_transparent_60%)]" />
                    <Music2 className="h-16 w-16 md:h-12 md:w-12 lg:h-16 lg:w-16 text-orange-600/70 dark:text-orange-400/70 relative z-10" />
                  </div>
                )}

                {/* Play Button Overlay */}
                <div className="absolute inset-0 bg-black/0 hover:bg-black/40 transition-all duration-300 flex items-center justify-center opacity-0 hover:opacity-100">
                  <Button
                    size="icon"
                    onClick={handlePlayPause}
                    className="h-12 w-12 md:h-10 md:w-10 lg:h-12 lg:w-12 rounded-full bg-white/90 hover:bg-white text-black shadow-2xl hover:shadow-3xl transition-all duration-300"
                  >
                    {globalPlayer.isPlaylistMode && globalPlayer.isPlaying ? (
                      <Pause className="h-6 w-6 md:h-5 md:w-5 lg:h-6 lg:w-6" />
                    ) : (
                      <Play className="h-6 w-6 md:h-5 md:w-5 lg:h-6 lg:w-6 ml-0.5" />
                    )}
                  </Button>
                </div>
              </div>

              {/* Playlist Info */}
              <div className="flex-1 space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300 text-xs">
                      Playlist
                    </Badge>
                    {playlist.isPublic && (
                      <Badge variant="outline" className="border-green-200 text-green-700 dark:border-green-800 dark:text-green-300 text-xs">
                        Public
                      </Badge>
                    )}
                  </div>

                  <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold tracking-tight bg-gradient-to-r from-orange-600 to-rose-600 bg-clip-text text-transparent dark:from-orange-400 dark:to-rose-400 leading-tight">
                    {playlist.name}
                  </h1>

                  {playlist.description && (
                    <p className="text-sm md:text-base text-muted-foreground leading-relaxed max-w-2xl">
                      {playlist.description}
                    </p>
                  )}
                </div>

                {/* Compact Stats */}
                <div className="flex flex-wrap items-center gap-4 text-xs md:text-sm text-muted-foreground">
                  <div className="flex items-center gap-1.5">
                    <Music2 className="h-3.5 w-3.5 text-orange-500" />
                    <span className="font-medium">{totalTracks} tracks</span>
                  </div>
                  {estimatedDuration > 0 && (
                    <div className="flex items-center gap-1.5">
                      <Clock className="h-3.5 w-3.5 text-rose-500" />
                      <span className="font-medium">~{estimatedDuration} min</span>
                    </div>
                  )}
                  <div className="flex items-center gap-1.5">
                    <Calendar className="h-3.5 w-3.5 text-blue-500" />
                    <span className="font-medium">Created {creationDate}</span>
                  </div>
                </div>

                {/* Compact Action Buttons */}
                <div className="flex flex-wrap items-center gap-3">
                  <Button
                    onClick={handlePlayPause}
                    className="bg-gradient-to-r from-orange-500 to-rose-500 hover:from-orange-600 hover:to-rose-600 text-white shadow-lg hover:shadow-xl transition-all duration-300 px-6"
                  >
                    {globalPlayer.isPlaylistMode && globalPlayer.isPlaying ? (
                      <>
                        <Pause className="h-4 w-4 mr-2" />
                        Pause
                      </>
                    ) : (
                      <>
                        <Play className="h-4 w-4 mr-2" />
                        Play All
                      </>
                    )}
                  </Button>

                  <Button
                    variant="outline"
                    onClick={() => setIsAddMusicDialogOpen(true)}
                    className="border-orange-200 hover:bg-orange-50 hover:border-orange-300 dark:border-orange-800 dark:hover:bg-orange-950/50 px-4"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Music
                  </Button>

                  <Button
                    variant="outline"
                    onClick={() => setIsAddNatureSoundsDialogOpen(true)}
                    className="border-emerald-200 hover:bg-emerald-50 hover:border-emerald-300 dark:border-emerald-800 dark:hover:bg-emerald-950/50 px-4"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Nature Sounds
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Tabbed Content Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <Card className="border-muted/50">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <CardHeader className="pb-3 px-4 pt-4">
              <div className="flex items-center justify-between">
                <TabsList className="grid w-auto grid-cols-2 bg-muted/50">
                  <TabsTrigger value="music" className="flex items-center gap-2 data-[state=active]:bg-orange-100 data-[state=active]:text-orange-800 dark:data-[state=active]:bg-orange-900/30 dark:data-[state=active]:text-orange-300">
                    <Music2 className="h-4 w-4" />
                    Music
                    <Badge variant="secondary" className="ml-1 text-xs">
                      {musics.length}
                    </Badge>
                  </TabsTrigger>
                  <TabsTrigger value="nature" className="flex items-center gap-2 data-[state=active]:bg-emerald-100 data-[state=active]:text-emerald-800 dark:data-[state=active]:bg-emerald-900/30 dark:data-[state=active]:text-emerald-300">
                    <Waves className="h-4 w-4" />
                    Natural Sounds
                    <Badge variant="secondary" className="ml-1 text-xs">
                      {natureSounds.length}
                    </Badge>
                  </TabsTrigger>
                </TabsList>
                <div className="flex items-center gap-2">
                  {activeTab === "music" ? (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setIsAddMusicDialogOpen(true)}
                      className="border-orange-200 hover:bg-orange-50 hover:border-orange-300 dark:border-orange-800 dark:hover:bg-orange-950/50"
                    >
                      <Plus className="h-3 w-3 mr-1" />
                      Add Music
                    </Button>
                  ) : (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setIsAddNatureSoundsDialogOpen(true)}
                      className="border-emerald-200 hover:bg-emerald-50 hover:border-emerald-300 dark:border-emerald-800 dark:hover:bg-emerald-950/50"
                    >
                      <Plus className="h-3 w-3 mr-1" />
                      Add Nature Sounds
                    </Button>
                  )}
                </div>
              </div>
            </CardHeader>

            <CardContent className="p-0">
              <TabsContent value="music" className="mt-0">
                {isLoadingMusics ? (
                  <div className="space-y-2 py-4 px-2">
                    {Array.from({ length: 5 }).map((_, i) => (
                      <div key={i} className="flex items-center gap-4 px-4 py-3 mx-2">
                        <Skeleton className="h-8 w-8 rounded-lg" />
                        <div className="flex-1 space-y-1.5">
                          <Skeleton className="h-4 w-3/4" />
                          <Skeleton className="h-3 w-1/2" />
                        </div>
                        <Skeleton className="h-6 w-16" />
                      </div>
                    ))}
                  </div>
                ) : musics.length === 0 ? (
                  <div className="text-center py-12 space-y-4 px-6">
                    <div className="mx-auto w-16 h-16 rounded-full bg-gradient-to-br from-orange-100 to-rose-100 dark:from-orange-900/30 dark:to-rose-900/30 flex items-center justify-center">
                      <Music2 className="h-8 w-8 text-orange-500/70" />
                    </div>
                    <div className="space-y-2">
                      <h3 className="text-lg font-semibold text-muted-foreground">No music yet</h3>
                      <p className="text-sm text-muted-foreground max-w-md mx-auto">
                        This playlist is empty. Start building your perfect soundtrack by adding some music tracks.
                      </p>
                    </div>
                    <Button
                      onClick={() => setIsAddMusicDialogOpen(true)}
                      className="bg-gradient-to-r from-orange-500 to-rose-500 hover:from-orange-600 hover:to-rose-600 text-white"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Add Your First Track
                    </Button>
                  </div>
                ) : (
                  <div className="py-2">
                    <div className="space-y-1">
                      <AnimatePresence>
                        {musics.map((music: any, index: number) => (
                          <MusicTrackItem
                            key={music.id}
                            music={music}
                            index={index}
                            isPlaying={globalPlayer.currentTrack?.id === music.id && globalPlayer.isPlaying}
                            onPlay={() => handleTrackPlay(music.id)}
                            playlistId={playlistId}
                          />
                        ))}
                      </AnimatePresence>
                    </div>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="nature" className="mt-0">
                {isLoadingNatureSounds ? (
                  <div className="space-y-2 py-4 px-2">
                    {Array.from({ length: 5 }).map((_, i) => (
                      <div key={i} className="flex items-center gap-4 px-4 py-3 mx-2">
                        <Skeleton className="h-8 w-8 rounded-lg" />
                        <div className="flex-1 space-y-1.5">
                          <Skeleton className="h-4 w-3/4" />
                          <Skeleton className="h-3 w-1/2" />
                        </div>
                        <Skeleton className="h-6 w-16" />
                      </div>
                    ))}
                  </div>
                ) : natureSounds.length === 0 ? (
                  <div className="text-center py-12 space-y-4 px-6">
                    <div className="mx-auto w-16 h-16 rounded-full bg-gradient-to-br from-emerald-100 to-teal-100 dark:from-emerald-900/30 dark:to-teal-900/30 flex items-center justify-center">
                      <Waves className="h-8 w-8 text-emerald-500/70" />
                    </div>
                    <div className="space-y-2">
                      <h3 className="text-lg font-semibold text-muted-foreground">No natural sounds yet</h3>
                      <p className="text-sm text-muted-foreground max-w-md mx-auto">
                        Add some relaxing natural sounds to enhance your focus environment.
                      </p>
                    </div>
                    <Button
                      onClick={() => setIsAddNatureSoundsDialogOpen(true)}
                      className="bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 text-white"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Add Your First Natural Sound
                    </Button>
                  </div>
                ) : (
                  <div className="py-2">
                    <div className="space-y-1">
                      <AnimatePresence>
                        {natureSounds.map((sound: any, index: number) => (
                          <NatureSoundItem
                            key={sound.id}
                            natureSound={sound}
                            index={index}
                            isPlaying={globalPlayer.currentTrack?.id === sound.id && globalPlayer.isPlaying}
                            onPlay={() => handleNatureSoundPlay(sound.id)}
                            playlistId={playlistId}
                          />
                        ))}
                      </AnimatePresence>
                    </div>
                  </div>
                )}
              </TabsContent>
            </CardContent>
          </Tabs>
        </Card>
      </motion.div>

      {/* Add Music Dialog */}
      <AddMusicDialog
        isOpen={isAddMusicDialogOpen}
        onOpenChange={setIsAddMusicDialogOpen}
        playlistId={playlistId}
        playlistName={playlist.name}
      />

      {/* Add Natural Sounds Dialog */}
      <AddNatureSoundsDialog
        isOpen={isAddNatureSoundsDialogOpen}
        onOpenChange={setIsAddNatureSoundsDialogOpen}
        playlistId={playlistId}
        playlistName={playlist.name}
      />

      {/* Audio Player is now handled by the GlobalAudioPlayer in the layout */}
    </div>
  )
}

function PlaylistDetailSkeleton() {
  return (
    <div className="space-y-6">
      {/* Header Skeleton */}
      <Card className="border-muted/50">
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-6 items-start">
            <Skeleton className="w-48 h-48 md:w-40 md:h-40 lg:w-48 lg:h-48 rounded-xl shrink-0" />
            <div className="flex-1 space-y-4">
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Skeleton className="h-5 w-16" />
                  <Skeleton className="h-5 w-12" />
                </div>
                <Skeleton className="h-8 md:h-10 lg:h-12 w-full max-w-md" />
                <Skeleton className="h-5 w-full max-w-2xl" />
              </div>
              <div className="flex items-center gap-4">
                <Skeleton className="h-3.5 w-20" />
                <Skeleton className="h-3.5 w-16" />
                <Skeleton className="h-3.5 w-24" />
              </div>
              <div className="flex items-center gap-3">
                <Skeleton className="h-10 w-24" />
                <Skeleton className="h-10 w-24" />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tracks Skeleton */}
      <Card className="border-muted/50">
        <CardHeader className="pb-3 px-4 pt-4">
          <Skeleton className="h-6 w-24" />
        </CardHeader>
        <CardContent className="p-0">
          <div className="space-y-1 p-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="flex items-center gap-3 p-3">
                <Skeleton className="h-10 w-10 rounded-lg" />
                <div className="flex-1 space-y-1.5">
                  <Skeleton className="h-3.5 w-3/4" />
                  <Skeleton className="h-3 w-1/2" />
                </div>
                <Skeleton className="h-6 w-16" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}