'use client';

import { <PERSON><PERSON>, <PERSON>alogContent, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { BarChart3, Clock, Target, Calendar, TrendingUp, LogIn, AlertTriangle, Cloud, ExternalLink } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { useUserStore } from '@/store/userStore';
import { useLocalPomodoroStore } from '@/lib/local-pomodoro-store';
import { useLocalTaskStore } from '@/lib/local-task-store';
import Link from 'next/link';
import { cn } from '@/lib/utils';

// Date formatting utilities
const formatDateDDMM = (date: Date): string => {
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  return `${day}/${month}`;
};

const formatDateDDMMYYYY = (date: Date): string => {
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();
  return `${day}/${month}/${year}`;
};

const getMonthAbbreviation = (date: Date): string => {
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  return months[date.getMonth()];
};

// Date calculation utilities for frontend timezone handling
const calculateDateRanges = () => {
  const now = new Date();

  // Today's range
  const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const todayEnd = new Date(todayStart);
  todayEnd.setDate(todayEnd.getDate() + 1);

  // Week range (Monday to Sunday)
  const currentDay = now.getDay(); // 0 = Sunday, 1 = Monday, etc.
  const daysFromMonday = currentDay === 0 ? 6 : currentDay - 1; // If Sunday, go back 6 days to Monday
  const weekStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  weekStart.setDate(weekStart.getDate() - daysFromMonday);

  const weekEnd = new Date(weekStart);
  weekEnd.setDate(weekEnd.getDate() + 6);
  weekEnd.setHours(23, 59, 59, 999);

  // Month range (1st to last day of current month)
  const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
  const monthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);

  return {
    todayStart: todayStart.toISOString(),
    todayEnd: todayEnd.toISOString(),
    weekStart: weekStart.toISOString(),
    weekEnd: weekEnd.toISOString(),
    monthStart: monthStart.toISOString(),
    monthEnd: monthEnd.toISOString(),
    // Also return the actual Date objects for tooltip formatting
    dates: {
      today: now,
      weekStart,
      weekEnd: new Date(weekEnd.getTime() - 1), // Subtract 1ms to get the actual last day
      monthStart,
      monthEnd: new Date(monthEnd.getTime() - 1) // Subtract 1ms to get the actual last day
    }
  };
};

interface QuickStatsDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  // Pre-fetched data props for authenticated users
  quickStatsData?: any;
  isLoading?: boolean;
  error?: any;
}

export function QuickStatsDialog({
  isOpen,
  onOpenChange,
  quickStatsData,
  isLoading = false,
  error
}: QuickStatsDialogProps) {
  const { isAuthenticated } = useUserStore();

  // Get local stats for unauthenticated users
  const { getQuickStats } = useLocalPomodoroStore();
  const { getTaskById } = useLocalTaskStore();
  const localStats = getQuickStats();

  // Helper function to populate session titles with task names for local data
  const populateLocalSessionTitles = (sessions: any[]) => {
    return sessions.map(session => {
      if (session.taskId && !isAuthenticated) {
        const task = getTaskById(session.taskId);
        if (task) {
          return {
            ...session,
            title: task.title
          };
        }
      }
      return session;
    });
  };

  // Format focus time (in minutes) to hours and minutes
  const formatFocusTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${remainingMinutes}m`;
    }
    return `${minutes}m`;
  };

  // Format time to HH:MM
  const formatTime = (timeString: string) => {
    try {
      const date = new Date(timeString);
      return date.toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return '--:--';
    }
  };

  // Get session type display info
  const getSessionTypeInfo = (type: string) => {
    switch (type.toLowerCase()) {
      case 'focus':
        return {
          label: 'Focus',
          color: 'bg-blue-500',
          textColor: 'text-blue-600 dark:text-blue-400',
          bgColor: 'bg-blue-500/10'
        };
      case 'shortbreak':
      case 'short_break':
        return {
          label: 'Short Break',
          color: 'bg-green-500',
          textColor: 'text-green-600 dark:text-green-400',
          bgColor: 'bg-green-500/10'
        };
      case 'longbreak':
      case 'long_break':
        return {
          label: 'Long Break',
          color: 'bg-purple-500',
          textColor: 'text-purple-600 dark:text-purple-400',
          bgColor: 'bg-purple-500/10'
        };
      default:
        return {
          label: 'Session',
          color: 'bg-gray-500',
          textColor: 'text-gray-600 dark:text-gray-400',
          bgColor: 'bg-gray-500/10'
        };
    }
  };

  // Extract data from appropriate source (local vs database)
  const getStatsData = () => {
    if (isAuthenticated) {
      // Use passed data for authenticated users
      return {
        todayFocusTime: quickStatsData?.todayFocusTime || 0,
        todayCompletedSessions: quickStatsData?.todayCompletedSessions || 0,
        todaySessions: quickStatsData?.todaySessions || [],
        weekTotalDuration: quickStatsData?.weekTotalDuration || 0,
        monthlyTotal: quickStatsData?.monthlyTotal || 0,
        hasData: true, // Always show stats for authenticated users
        dateRange: quickStatsData?.dateRange
      };
    } else {
      // Use local data for unauthenticated users
      return {
        todayFocusTime: localStats.todayFocusTime,
        todayCompletedSessions: localStats.todayCompletedSessions,
        todaySessions: populateLocalSessionTitles(localStats.todaySessions),
        weekTotalDuration: localStats.weekTotalDuration,
        monthlyTotal: localStats.monthlyTotal,
        hasData: true, // Always show stats, even with 0 values
        dateRange: localStats.dateRange
      };
    }
  };

  const statsData = getStatsData();

  const renderLocalStatsPrompt = () => (
    <div className="space-y-2 p-2">
      <Card className="border-0 bg-gradient-to-br from-red-50/60 via-red-100/40 to-red-50/30 dark:from-red-950/20 dark:via-red-900/15 dark:to-red-950/10">
        <CardContent className="py-0 px-3">
          <div className="flex items-center justify-between gap-2">
            <div className="flex items-center gap-2">
              <div className="flex-shrink-0 w-6 h-6 rounded-full bg-red-500/10 dark:bg-red-400/10 flex items-center justify-center">
                <Cloud className="h-3 w-3 text-red-600 dark:text-red-400" />
              </div>
              <div className="space-y-1">
                <h4 className="text-xs font-semibold text-red-600 dark:text-red-400">Sync Progress</h4>
                <p className="text-xs text-red-500/70 dark:text-red-300/70">Sign in to save sessions across devices</p>
              </div>
            </div>
            <Link href="/auth/sign-in">
              <Button size="sm" className="h-7 text-xs font-medium bg-red-600 hover:bg-red-700 dark:bg-red-500 dark:hover:bg-red-600 transition-colors duration-200" aria-label="Sign in to sync your focus data across devices">
                <LogIn className="h-3 w-3 mr-1.5" />
                Sign In
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderSkeletonContent = () => (
    <div className="space-y-4 animate-in fade-in-50 duration-300">
      {/* Header skeleton */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <div className="h-4 w-32 bg-gradient-to-r from-muted/30 to-muted/10 rounded-md animate-pulse" />
          <div className="h-3 w-20 bg-gradient-to-r from-muted/20 to-muted/5 rounded animate-pulse" />
        </div>
        <div className="h-7 w-16 bg-gradient-to-r from-muted/25 to-muted/10 rounded-md animate-pulse" />
      </div>

      {/* Metrics grid skeleton */}
      <div className="grid grid-cols-2 gap-3">
        {Array.from({ length: 4 }).map((_, index) => (
          <div
            key={index}
            className="rounded-lg border border-border/50 p-3 bg-gradient-to-br from-background via-muted/5 to-muted/10 shadow-sm"
            style={{ animationDelay: `${index * 100}ms` }}
          >
            <div className="flex items-center justify-between mb-2">
              <div className="rounded-md p-1.5 bg-gradient-to-br from-muted/20 to-muted/10 animate-pulse">
                <div className="h-3 w-3 bg-muted/30 rounded" />
              </div>
            </div>
            <div className="space-y-1">
              <div className="h-6 w-14 bg-gradient-to-r from-muted/30 to-muted/10 rounded animate-pulse" />
              <div className="h-3 w-16 bg-gradient-to-r from-muted/20 to-muted/5 rounded animate-pulse" />
            </div>
          </div>
        ))}
      </div>

      {/* Recent sessions skeleton */}
      <div className="pt-3 border-t border-border/50">
        <div className="flex items-center justify-between mb-3">
          <div className="h-3 w-32 bg-gradient-to-r from-muted/25 to-muted/10 rounded animate-pulse" />
          <div className="h-3 w-12 bg-gradient-to-r from-muted/20 to-muted/5 rounded animate-pulse" />
        </div>
        <div className="space-y-2 max-h-20">
          {Array.from({ length: 2 }).map((_, index) => (
            <div
              key={index}
              className="rounded-md p-2 border border-border/30 bg-gradient-to-r from-background via-muted/5 to-muted/10 shadow-sm"
              style={{ animationDelay: `${(index + 4) * 100}ms` }}
            >
              <div className="flex items-start justify-between gap-2">
                <div className="flex-1 min-w-0 space-y-1">
                  <div className="flex items-center gap-1.5">
                    <div className="w-1.5 h-1.5 rounded-full bg-gradient-to-r from-muted/30 to-muted/10 animate-pulse" />
                    <div className="h-3 w-24 bg-gradient-to-r from-muted/25 to-muted/10 rounded animate-pulse" />
                  </div>
                  <div className="flex items-center gap-1.5">
                    <div className="w-2.5 h-2.5 bg-gradient-to-r from-muted/20 to-muted/5 rounded animate-pulse" />
                    <div className="h-2.5 w-20 bg-gradient-to-r from-muted/20 to-muted/5 rounded animate-pulse" />
                    <div className="h-2.5 w-8 bg-gradient-to-r from-muted/20 to-muted/5 rounded animate-pulse" />
                  </div>
                </div>
                <div className="w-4 h-4 rounded-full bg-gradient-to-br from-muted/25 to-muted/10 animate-pulse" />
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderStatsContent = () => {
    // Filter to get all focus sessions (for consistent counting)
    const allFocusSessions = statsData.todaySessions.filter((session: any) =>
      session.type.toLowerCase() === 'focus'
    );

    // Get current date ranges for tooltip formatting
    const currentDateRanges = calculateDateRanges();

    const metrics = [
      {
        label: "Today's Focus",
        value: formatFocusTime(statsData.todayFocusTime),
        subValue: null,
        icon: Clock,
        color: "text-green-600 dark:text-green-400",
        bgColor: "bg-green-500/10"
      },
      {
        label: "Focus Sessions",
        value: allFocusSessions.length.toString(),
        subValue: null,
        icon: Target,
        color: "text-blue-600 dark:text-blue-400",
        bgColor: "bg-blue-500/10"
      },
      {
        label: "Week Total",
        value: formatFocusTime(statsData.weekTotalDuration),
        subValue: null,
        icon: TrendingUp,
        color: "text-purple-600 dark:text-purple-400",
        bgColor: "bg-purple-500/10",
        tooltip: `${formatDateDDMM(currentDateRanges.dates.weekStart)} - ${formatDateDDMM(currentDateRanges.dates.today)} ${getMonthAbbreviation(currentDateRanges.dates.today)}`
      },
      {
        label: "Monthly Total",
        value: formatFocusTime(statsData.monthlyTotal || 0),
        subValue: null,
        icon: Calendar,
        color: "text-emerald-600 dark:text-emerald-400",
        bgColor: "bg-emerald-500/10",
        tooltip: `${formatDateDDMMYYYY(currentDateRanges.dates.monthStart)} - ${formatDateDDMMYYYY(currentDateRanges.dates.monthEnd)}`
      }
    ];

    return (
      <div className="space-y-4">
        {/* Metrics grid */}
        <div className="grid grid-cols-2 gap-3">
          {metrics.map((metric, index) => {
            const IconComponent = metric.icon;
            const hasTooltip = metric.tooltip;

            const metricCard = (
              <div
                key={metric.label}
                className={cn(
                  "group relative rounded-lg border border-border/50 p-3 transition-all duration-300 hover:shadow-md hover:shadow-black/5 hover:border-border/80",
                  "bg-gradient-to-br from-background via-background/80 to-muted/10",
                  "hover:scale-[1.01] active:scale-[0.99]",
                  hasTooltip && "cursor-help"
                )}
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="flex items-center justify-between mb-2">
                  <div className={cn(
                    "rounded-md p-1.5 transition-all duration-200 group-hover:scale-105",
                    metric.bgColor
                  )}>
                    <IconComponent className={cn("h-3 w-3 transition-colors duration-200", metric.color)} />
                  </div>
                </div>
                <div className="space-y-1">
                  <div className="flex items-baseline gap-1">
                    <p className="text-xl font-bold tracking-tight text-foreground">{metric.value}</p>
                    {metric.subValue && (
                      <span className="text-xs text-muted-foreground font-medium">
                        {metric.subValue}
                      </span>
                    )}
                  </div>
                  <p className="text-xs text-muted-foreground font-medium">{metric.label}</p>
                </div>
              </div>
            );

            // Wrap with tooltip if available
            if (hasTooltip) {
              return (
                <Tooltip key={metric.label}>
                  <TooltipTrigger asChild>
                    {metricCard}
                  </TooltipTrigger>
                  <TooltipContent side="top" className="max-w-48 text-center">
                    <p className="text-xs leading-relaxed">{metric.tooltip}</p>
                  </TooltipContent>
                </Tooltip>
              );
            }

            return metricCard;
          })}
        </div>

        {/* View All button - repositioned to avoid header overlap */}
        <div className="text-center">
          <Link href="/dashboard/analytics">
            <Button
              variant="ghost"
              size="sm"
              className="text-xs h-7 px-3 hover:bg-accent/50 transition-colors duration-200"
              aria-label="View detailed analytics dashboard"
            >
              <ExternalLink className="h-3 w-3 mr-1" />
              View All
            </Button>
          </Link>
        </div>

        {(() => {
          // Use the same focus sessions array for consistency
          const focusSessions = allFocusSessions;

          return focusSessions.length > 0 && (
            <div className="pt-2 border-t border-border/50">
              <div className="flex items-center justify-between mb-2">
                <h4 className="text-xs font-semibold text-foreground">Recent Focus Sessions</h4>
                <span className="text-xs text-muted-foreground">
                  {focusSessions.length} today
                </span>
              </div>
              <div className="space-y-1.5 max-h-20 overflow-y-auto scrollbar-thin scrollbar-thumb-muted-foreground/20 scrollbar-track-transparent">
                {focusSessions.slice(0, 3).map((session: any, sessionIndex: number) => {
                const typeInfo = getSessionTypeInfo(session.type);
                const startTime = formatTime(session.startTime);
                const endTime = formatTime(session.endTime);

                return (
                  <div
                    key={session.id || sessionIndex}
                    className={cn(
                      "group relative rounded-md p-2 transition-all duration-200",
                      "hover:bg-muted/50 hover:shadow-sm border hover:border-border/30",
                      "bg-gradient-to-r from-background to-muted/20",
                      // Special styling for interrupted sessions
                      session.interrupted
                        ? "border-amber-200/50 dark:border-amber-800/30 bg-gradient-to-r from-amber-50/30 to-amber-100/20 dark:from-amber-950/20 dark:to-amber-900/10"
                        : "border-transparent"
                    )}
                  >
                    <div className="flex items-start justify-between gap-2">
                      {/* Session Info */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-1.5 mb-0.5">
                          <div className={cn("w-1.5 h-1.5 rounded-full flex-shrink-0", typeInfo.color)} />
                          <span className="text-xs font-medium text-foreground truncate">
                            {session.title || `${typeInfo.label} Session`}
                          </span>
                          {/* Interrupted indicator */}
                          {session.interrupted && (
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <div className="flex items-center justify-center w-3 h-3 rounded-full bg-amber-500/15 flex-shrink-0">
                                  <AlertTriangle className="w-2 h-2 text-amber-600 dark:text-amber-400" />
                                </div>
                              </TooltipTrigger>
                              <TooltipContent side="top" className="text-xs">
                                <span>Session was interrupted</span>
                              </TooltipContent>
                            </Tooltip>
                          )}
                        </div>

                        {/* Timeline */}
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                          <Clock className="w-2.5 h-2.5 flex-shrink-0" />
                          <span className="font-mono text-xs">
                            {startTime} - {endTime}
                          </span>
                          <span className="text-muted-foreground/60">•</span>
                          <span className="font-medium">
                            {session.duration}m
                          </span>
                        </div>
                      </div>

                      {/* Status Indicator */}
                      <div className="flex items-center gap-1 flex-shrink-0">
                        <div className={cn(
                          "flex items-center justify-center w-4 h-4 rounded-full text-xs font-medium transition-colors",
                          session.completed
                            ? "bg-green-500/15 text-green-600 dark:text-green-400"
                            : session.interrupted
                            ? "bg-red-500/15 text-red-600 dark:text-red-400"
                            : "bg-amber-500/15 text-amber-600 dark:text-amber-400"
                        )}>
                          {session.completed ? "✓" : session.interrupted ? "✕" : "○"}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>

              {/* Show more indicator if there are more focus sessions */}
              {focusSessions.length > 3 && (
                <div className="mt-1.5 text-center">
                  <Link href="/dashboard">
                    <Button variant="ghost" size="sm" className="text-xs h-5 px-2 text-muted-foreground hover:text-foreground">
                      +{focusSessions.length - 3} more sessions
                    </Button>
                  </Link>
                </div>
              )}
            </div>
          );
        })()}

        {/* Show local data sync prompt for unauthenticated users */}
        {!isAuthenticated && (
          <div className="pt-2 border-t border-border/50">
            {renderLocalStatsPrompt()}
          </div>
        )}
      </div>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="w-full max-w-sm mx-auto bg-background/95 backdrop-blur-md border-white/20 p-1 gap-0 max-h-[85vh] overflow-hidden">
        <DialogHeader className="px-3 pt-3 pb-2 space-y-1">
          <DialogTitle className="text-lg md:text-xl font-bold flex items-center gap-1.5 bg-clip-text text-transparent bg-gradient-to-r from-orange-500 via-red-500 to-rose-600 pb-3">
            <BarChart3 className="h-4 w-4 text-rose-600 dark:text-rose-500" />
            Focus Statistics
          </DialogTitle>
          {/* <p className="text-xs text-muted-foreground pl-6">Your Focus Stats</p> */}
        </DialogHeader>

        <div className="px-3 pb-3 overflow-y-auto max-h-[calc(85vh-3rem)]">
          {isAuthenticated && isLoading ? (
            renderSkeletonContent()
          ) : isAuthenticated && (error || !quickStatsData) ? (
            <div className="space-y-3 text-center p-2">
              <div className="space-y-1.5">
                <div className="mx-auto w-10 h-10 bg-gradient-to-br from-muted/20 to-muted/10 rounded-full flex items-center justify-center">
                  <BarChart3 className="h-5 w-5 text-muted-foreground" />
                </div>
                <h3 className="font-semibold text-xs">Unable to Load Stats</h3>
                <p className="text-xs text-muted-foreground leading-relaxed">
                  Please try again or start a focus session.
                </p>
              </div>
            </div>
          ) : (
            renderStatsContent()
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
