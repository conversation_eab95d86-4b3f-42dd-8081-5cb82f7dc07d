import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { InferRequestType, InferResponseType } from "hono";
import client from "@/lib/trpc";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

// Get all music tracks
export type GetMusics_ResponseType = InferResponseType<
  (typeof client.api.musics)["$get"],
  200
>;

export type GetMusics_ResponseTypeSuccess = Extract<
  GetMusics_ResponseType,
  { data: object }
>["data"];

export const useGetMusics = (filters?: {
  isPublic?: boolean;
  playlistId?: string;
}) => {
  return useQuery({
    queryKey: ["musics", filters],
    queryFn: async () => {
      const queryParams = new URLSearchParams();
      
      if (filters?.isPublic !== undefined) {
        queryParams.append("isPublic", String(filters.isPublic));
      }
      
      if (filters?.playlistId) {
        queryParams.append("playlistId", filters.playlistId);
      }
      
      const queryString = queryParams.toString();
      
      let response;
      if (queryString) {
        response = await client.api.musics.$get({
          query: { 
            isPublic: filters?.isPublic !== undefined ? String(filters.isPublic) : undefined,
            playlistId: filters?.playlistId
          }
        });
      } else {
        response = await client.api.musics.$get();
      }
      
      if (!response.ok) {
        throw new Error("Failed to fetch music tracks");
      }
      const { data } = await response.json();
      return data;
    },
  });
};

// Get user music tracks (limited fields for dashboard users)
export type GetUserMusics_ResponseType = InferResponseType<
  (typeof client.api.musics.user)["$get"],
  200
>;

export type GetUserMusics_ResponseTypeSuccess = Extract<
  GetUserMusics_ResponseType,
  { data: object }
>["data"];

export const useGetUserMusics = (filters?: {
  isPublic?: boolean;
  playlistId?: string;
}) => {
  return useQuery({
    queryKey: ["userMusics", filters],
    queryFn: async () => {
      const queryParams = new URLSearchParams();

      if (filters?.isPublic !== undefined) {
        queryParams.append("isPublic", String(filters.isPublic));
      }

      if (filters?.playlistId) {
        queryParams.append("playlistId", filters.playlistId);
      }

      const queryString = queryParams.toString();

      let response;
      if (queryString) {
        response = await client.api.musics.user.$get({
          query: {
            isPublic: filters?.isPublic !== undefined ? String(filters.isPublic) : undefined,
            playlistId: filters?.playlistId
          }
        });
      } else {
        response = await client.api.musics.user.$get();
      }

      if (!response.ok) {
        throw new Error("Failed to fetch user music tracks");
      }
      const { data } = await response.json();
      return data;
    },
  });
};

// Get single music track
type GetMusic_ResponseType = InferResponseType<
  (typeof client.api.musics)[":id"]["$get"],
  200
>;

export type GetMusic_ResponseTypeSuccess = Extract<
  GetMusic_ResponseType,
  { data: object }
>["data"];

export const useGetMusic = (id?: string) => {
  return useQuery({
    queryKey: ["musics", { id }],
    queryFn: async () => {
      if (!id) throw new Error("No music ID provided");

      const response = await client.api.musics[":id"]["$get"]({
        param: { id },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch music track");
      }

      const { data } = await response.json();
      return data;
    },
    enabled: !!id,
  });
};

// Create music track
interface CreateMusicSuccessResponse {
  data: {
    id: string;
    [key: string]: unknown;
  };
}

type CreateMusicRequest = InferRequestType<
  (typeof client.api.musics)["$post"]
>;

export const useCreateMusic = () => {
  const router = useRouter();
  const queryClient = useQueryClient();

  const mutation = useMutation<
    CreateMusicSuccessResponse,
    Error,
    CreateMusicRequest
  >({
    mutationFn: async ({ form }) => {
      const response = await client.api.musics.$post({ form });

      if (!response.ok) {
        throw new Error("Failed to create music track");
      }

      return await response.json();
    },
    onSuccess: ({ data }) => {
      toast.success("Music created successfully");
      router.refresh();
      queryClient.invalidateQueries({ queryKey: ["musics"] });
    },
    onError: (error) => {
      toast.error(`Failed to create music track: ${error.message}`);
    },
  });

  return mutation;
};

// Update music track
type UpdateMusic_ResponseType = InferResponseType<
  (typeof client.api.musics)[":id"]["$patch"],
  200
>;

export type UpdateMusic_ResponseTypeSuccess = Extract<
  UpdateMusic_ResponseType,
  { data: object }
>["data"];

type UpdateMusicRequest = InferRequestType<
  (typeof client.api.musics)[":id"]["$patch"]
>;

export const useUpdateMusic = () => {
  const queryClient = useQueryClient();
  
  return useMutation<
    UpdateMusic_ResponseType,
    Error,
    UpdateMusicRequest
  >({
    mutationFn: async (variables) => {
      const { form, param } = variables;

      if (!param?.id) {
        throw new Error("No music ID provided");
      }

      const response = await client.api.musics[":id"]["$patch"]({
        form,
        param: { id: param.id },
      });

      if (!response.ok) {
        throw new Error(`Failed to update music track. Status: ${response.status}`);
      }

      return await response.json();
    },
    onSuccess: ({ data }) => {
      toast.success("Music track updated successfully");
      const musicId = data?.id;
      if (musicId) {
        queryClient.invalidateQueries({ queryKey: ["musics", { id: musicId }] });
      }
      queryClient.invalidateQueries({ queryKey: ["musics"] });
    },
    onError: (error) => {
      toast.error(`Failed to update music track: ${error.message}`);
    },
  });
};

// Delete music track
type DeleteMusic_ResponseType = InferResponseType<
  (typeof client.api.musics)[":id"]["$delete"],
  200
>;

export const useDeleteMusic = () => {
  const queryClient = useQueryClient();
  const router = useRouter();

  return useMutation<
    DeleteMusic_ResponseType,
    Error,
    { id: string }
  >({
    mutationFn: async ({ id }) => {
      if (!id) {
        throw new Error("No music ID provided");
      }

      const response = await client.api.musics[":id"]["$delete"]({
        param: { id },
      });

      if (!response.ok) {
        throw new Error("Failed to delete music track");
      }

      return await response.json();
    },
    onSuccess: () => {
      toast.success("Music track deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["musics"] });
    },
    onError: (error) => {
      toast.error(`Failed to delete music track: ${error.message}`);
    },
  });
}; 