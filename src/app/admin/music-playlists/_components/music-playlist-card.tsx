"use client";

import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { MoreHorizontal, Play, Music2, Video, Edit, Trash, ExternalLink } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { useRouter } from "next/navigation";
import { useDeleteAdminMusicPlaylist } from "@schemas/MusicPlaylist/music-playlist-admin-query";
import { toast } from "sonner";
import Image from "next/image";
import { GetAdminMusicPlaylists_ResponseTypeSuccess } from "@schemas/MusicPlaylist/music-playlist-admin-query";
import { useState } from "react";
import { MusicPlaylistFormSheet } from "./music-playlist-form-sheet";
import { ConfirmationDialog } from "@/components/ui/confirmation-dialog";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { formatGenreName, getGenreColorClass, truncateGenres } from "@/lib/genre-utils";

export function MusicPlaylistCard({ playlist }: { playlist: GetAdminMusicPlaylists_ResponseTypeSuccess[0] }) {
  const router = useRouter();
  const deleteMusicPlaylist = useDeleteAdminMusicPlaylist();
  const [isEditSheetOpen, setIsEditSheetOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const handleDelete = async () => {
    try {
      await deleteMusicPlaylist.mutateAsync({ id: playlist.id });
      toast.success("Music playlist deleted successfully");
      setIsDeleteDialogOpen(false);
    } catch (error) {
      console.error(error);
      toast.error("Failed to delete music playlist");
    }
  };

  const handlePlayAll = () => router.push(`/admin/music-playlists/${playlist.id}`);
  const handleEditDetails = () => setIsEditSheetOpen(true);

  const renderMediaCounts = () => (
    <div className="flex items-center gap-3">
      <div className="flex items-center gap-1.5">
        <Video className="h-3.5 w-3.5 text-indigo-600 dark:text-indigo-400" />
        <span className="text-xs font-medium">{playlist.videos.length}</span>
      </div>
      <div className="flex items-center gap-1.5">
        <Music2 className="h-3.5 w-3.5 text-blue-600 dark:text-blue-400" />
        <span className="text-xs font-medium">{playlist.musics.length}</span>
      </div>
    </div>
  );

  const renderGenres = () => {
    if (!playlist.genres || playlist.genres.length === 0) return null;

    const { visible, remaining } = truncateGenres(playlist.genres, 2);

    return (
      <div className="flex flex-wrap gap-1.5">
        {visible.map((genre) => (
          <Badge
            key={genre}
            variant="outline"
            className={cn("text-xs", getGenreColorClass(genre))}
          >
            {formatGenreName(genre)}
          </Badge>
        ))}
        {remaining > 0 && (
          <Badge variant="outline" className="text-xs bg-gray-100 text-gray-600 dark:bg-gray-900/30 dark:text-gray-400">
            +{remaining}
          </Badge>
        )}
      </div>
    );
  };

  const renderBadges = () => (
    <div className="flex flex-wrap gap-2">
      {playlist.isPublic && (
        <Badge variant="outline" className="text-xs bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
          Public
        </Badge>
      )}
      {playlist.isDefault && (
        <Badge variant="outline" className="text-xs bg-indigo-50 dark:bg-indigo-900/20 border-indigo-200 dark:border-indigo-800">
          Default
        </Badge>
      )}
    </div>
  );

  const creationDate = new Date(playlist.createdAt).toLocaleDateString(undefined, {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });

  return (
    <>
      <Card 
        className={cn(
          "group relative overflow-hidden transition-all hover:shadow-lg focus-within:shadow-lg border-muted/80",
          isHovered && "ring-2 ring-blue-500/50 dark:ring-blue-500/30"
        )}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div className="relative aspect-video">
          {playlist.imageUrl ? (
            <Image
              src={playlist.imageUrl}
              alt={playlist.name || 'Music playlist image'}
              fill
              className={cn(
                "object-cover transition-transform duration-500",
                isHovered && "scale-105"
              )}
              sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
              loading="lazy"
            />
          ) : (
            <div className="w-full h-full bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-blue-900/30 dark:to-indigo-900/30 flex items-center justify-center">
              <Music2 className="h-12 w-12 text-blue-600/70 dark:text-blue-400/70" />
            </div>
          )}
          
          {/* Overlay with play button */}
          <div 
            className={cn(
              "absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex items-center justify-center",
              "opacity-0 group-hover:opacity-100 transition-opacity duration-300"
            )}
          >
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={isHovered ? { y: 0, opacity: 1 } : { y: 20, opacity: 0 }}
              transition={{ duration: 0.3 }}
            >
              <Button
                variant="secondary"
                size="sm"
                onClick={handlePlayAll}
                className="shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                aria-label={`Play all tracks in ${playlist.name}`}
              >
                <Play className="mr-2 h-4 w-4" />
                Show Playlist
              </Button>
            </motion.div>
          </div>
          
          {/* Badge indicators */}
          <div className="absolute top-2 right-2 flex gap-1.5">
            {playlist.isDefault && (
              <div className="bg-indigo-600/90 text-white text-xs px-1.5 py-0.5 rounded-md shadow-md">
                Default
              </div>
            )}
            {playlist.isPublic && (
              <div className="bg-blue-600/90 text-white text-xs px-1.5 py-0.5 rounded-md shadow-md">
                Public
              </div>
            )}
          </div>
        </div>

        <CardContent className="p-4">
          <div className="flex items-start justify-between gap-2">
            <div className="space-y-1.5 flex-1 min-w-0">
              <h3 className="font-semibold line-clamp-1 group-hover:text-primary transition-colors text-base sm:text-lg cursor-pointer"
                onClick={
                  () => router.push(`/admin/music-playlists/${playlist.id}`)
                }
              >
                {playlist.name}
              </h3>
              <p className="text-sm text-muted-foreground line-clamp-2 min-h-[2.5rem] sm:min-h-[3rem]">
                {playlist.description || "No description"}
              </p>

              {/* Genres */}
              {renderGenres()}

              <p className="text-xs text-muted-foreground mt-1">
                Created {creationDate}
              </p>
            </div>
            <DropdownMenu modal={false}>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className={cn(
                    "h-8 w-8 shrink-0 rounded-full transition-opacity",
                    "opacity-0 group-hover:opacity-100 focus:opacity-100",
                    "focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
                  )}
                  aria-label={`More options for ${playlist.name}`}
                >
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuItem
                  onClick={handleEditDetails}
                  className="cursor-pointer focus:bg-accent"
                >
                  <Edit className="mr-2 h-4 w-4 text-amber-600 dark:text-amber-400" />
                  <span>Edit Details</span>
                </DropdownMenuItem>
                {!playlist.isDefault && (
                  <DropdownMenuItem
                    className="text-destructive cursor-pointer focus:text-destructive focus:bg-destructive/10"
                    onClick={() => setIsDeleteDialogOpen(true)}
                  >
                    <Trash className="mr-2 h-4 w-4" />
                    <span>Delete Playlist</span>
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardContent>

        <CardFooter className="p-4 pt-0 flex flex-wrap gap-2 text-xs sm:text-sm justify-between items-center">
          {renderMediaCounts()}
          {renderBadges()}
        </CardFooter>
      </Card>

      {/* Edit Music Playlist Sheet */}
      <MusicPlaylistFormSheet 
        playlistId={playlist.id}
        open={isEditSheetOpen} 
        onOpenChange={setIsEditSheetOpen}
        onSuccess={() => {
          setIsEditSheetOpen(false);
        }}
      />

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        title="Delete Music Playlist"
        description={`Are you sure you want to delete "${playlist.name}"? This action cannot be undone.`}
        onConfirm={handleDelete}
        isLoading={deleteMusicPlaylist.isPending}
      />
    </>
  );
} 