'use client';

import { useCallback, useState, useRef, useMemo, useEffect, memo } from 'react';
import { usePomodoro } from '@/hooks/usePomodoro';
import { usePomodoroStore, TimerPhase } from '@/lib/pomodoro-store';

import { useTimerResize } from './use-timer-resize';
import { useTimerDragManager } from './timer-drag-manager';
import { TimerMainDisplay } from './main-display';
import { CircularTimer } from './circular-timer';
import { TimerProgressBar } from './timer-progress-bar';
import { TimerContainer } from './timer-container';
import { TimerInteractionManager } from './timer-interaction-manager';
import { TimerAutoBehaviors } from './timer-auto-behaviors';
import { TimerControls } from './timer-controls';
import type { CustomPosition } from './common/types';
import { SignInPrompt } from './sign-in-prompt';
import { useUserStore } from '@/store/userStore';
import { useRouter } from 'next/navigation';

// Wrap the TimerDisplay component with memo to prevent unnecessary re-renders
export const TimerDisplay = memo(function TimerDisplay() {
  const router = useRouter();
  const { isAuthenticated, preferences } = useUserStore();
  const [showSignInPrompt, setShowSignInPrompt] = useState(false);

  // Helper function to safely get dismissal status
  const isSignInPromptDismissed = useMemo(() => {
    return preferences?.ui?.dismissedSignInPrompt ?? false;
  }, [preferences?.ui?.dismissedSignInPrompt]);

  // Handle session completion for unauthenticated users
  const handleSessionComplete = useCallback((phase: string, isUserAuthenticated: boolean) => {
    // Only show prompt for unauthenticated users who haven't dismissed it
    if (!isUserAuthenticated && !isSignInPromptDismissed) {
      // Only show for focus sessions (pomodoro), not breaks
      if (phase === 'pomodoro') {
        setShowSignInPrompt(true);
      }
    }
  }, [isSignInPromptDismissed]);

  const {
    formattedTime,
    phaseLabel,
    progressPercentage,
    isRunning,
    startTimer,
    pauseTimer,
    resetTimer,
    currentPhase,
    pomodoroCount
  } = usePomodoro({ onSessionComplete: handleSessionComplete });

  // Get the values directly since usePomodoro now returns computed values
  const timeDisplay = formattedTime;
  const currentPhaseLabel = phaseLabel;
  const currentProgress = progressPercentage;

  // Get properties from the pomodoro store
  const timerPosition = usePomodoroStore((state) => state.timerPosition);
  const timerSettings = usePomodoroStore((state) => state.timerSettings);
  const isFullscreen = usePomodoroStore((state) => state.isFullscreen);
  const timerColor = usePomodoroStore((state) => state.timerColor);
  const timerOpacity = usePomodoroStore((state) => state.timerOpacity);
  const autoStartPomodoros = usePomodoroStore((state) => state.autoStartPomodoros);
  const timerUIStyle = usePomodoroStore((state) => state.timerUIStyle || 'default');
  const setTimerPosition = usePomodoroStore((state) => state.setTimerPosition);
  const currentTask = usePomodoroStore((state) => state.currentTask);
  const showProgressBar = usePomodoroStore((state) => state.showProgressBar);


  const [showControls, setShowControls] = useState(false);
  const [customPosition, setCustomPosition] = useState<CustomPosition | null>(null);
  const timerRef = useRef<HTMLDivElement>(null);
  const timerDisplayRef = useRef<HTMLDivElement>(null);

  // Use our custom resize hook
  const {
    isResizing,
    timerSize,
    fontSize,
    showResizeHandles,
    isResizeHandleHovered,
    isResizePreview,
    startResize,
    handleResizeMove,
    endResize,
    showHandles,
    hideHandles,
    handleResizeHandleMouseEnter,
    handleResizeHandleMouseLeave,
    applyPreset,
    cleanup: cleanupResize
  } = useTimerResize(timerRef);

  // Use the drag manager hook
  const {
    isDragging,
    showPositionIndicator,
    handleDragStartMouse,
    handleDragStartTouch,
    handleDoubleClick,
    adjustTimerPosition
  } = useTimerDragManager({
    timerRef,
    customPosition,
    setCustomPosition,
    isResizing,
    showControls,
    setShowControls,
    hideHandles,
    timerSize,
    setTimerPosition
  });



  // Handle fullscreen change
  useEffect(() => {
    // When fullscreen state changes, adjust timer position
    if (customPosition) {
      requestAnimationFrame(adjustTimerPosition);
    }
  }, [isFullscreen, adjustTimerPosition, customPosition]);

  // Handle window resize events with debouncing
  useEffect(() => {
    if (!customPosition) return;

    let resizeTimeout: NodeJS.Timeout;

    const handleResize = () => {
      // Clear the timeout if there's one pending
      if (resizeTimeout) {
        clearTimeout(resizeTimeout);
      }

      // Set a new timeout for performance
      resizeTimeout = setTimeout(() => {
        requestAnimationFrame(adjustTimerPosition);
      }, 100);
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
      if (resizeTimeout) {
        clearTimeout(resizeTimeout);
      }
    };
  }, [customPosition, adjustTimerPosition]);



  // Handle mouse resize - optimized with memoization
  const handleResizeStartMouse = useCallback((e: React.MouseEvent) => {
    // Prevent default to avoid text selection
    e.preventDefault();
    startResize(e);
  }, [startResize]);

  // Handle touch resize - optimized
  const handleResizeStartTouch = useCallback((e: React.TouchEvent) => {
    e.preventDefault();
    startResize(e);
  }, [startResize]);

  // Effect to add mouse move and up event listeners for resize - optimized with throttling
  useEffect(() => {
    if (isResizing) {
      let lastFrameTime = 0;
      const FRAME_RATE = 1000 / 60; // Target 60 FPS

      const handleResizeMoveMouse = (e: MouseEvent) => {
        if (!isResizing) return;

        e.preventDefault();

        // Throttling to ensure smooth animation at 60fps target
        const now = performance.now();
        const elapsed = now - lastFrameTime;

        if (elapsed > FRAME_RATE) {
          lastFrameTime = now;

          requestAnimationFrame(() => {
            if (isResizing) { // Double-check we're still resizing
              handleResizeMove(e.clientX);
            }
          });
        }
      };

      // Use passive: false for mouseup to prevent potential browser issues
      window.addEventListener('mousemove', handleResizeMoveMouse, { passive: false });
      window.addEventListener('mouseup', endResize);

      return () => {
        window.removeEventListener('mousemove', handleResizeMoveMouse);
        window.removeEventListener('mouseup', endResize);
      };
    }
  }, [isResizing, handleResizeMove, endResize]);

  // Keyboard navigation for resizing - remains mostly the same
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Only handle keyboard events if the timer has focus
      if (!document.activeElement || !timerRef.current?.contains(document.activeElement)) {
        return;
      }

      if (e.key === 'Escape') {
        if (isResizing) {
          endResize();
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [isResizing, endResize]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cleanupResize();
    };
  }, [cleanupResize]);

  // Handle timer toggle
  const handleTimerToggle = useCallback(() => {
    if (isRunning) {
      pauseTimer();
    } else {
      startTimer();
    }
  }, [isRunning, pauseTimer, startTimer]);

  // Keyboard event handler for accessibility
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    switch (e.key) {
      case 'Enter':
      case ' ':
        handleTimerToggle();
        e.preventDefault();
        break;
    }
  }, [handleTimerToggle]);

  // Wrapper functions to prevent dragging when settings dialog is open
  const handleDragStartMouseWrapper = useCallback((e: React.MouseEvent) => {
    // Check if the target is a control element (button, resize handle, etc.)
    const target = e.target as HTMLElement;
    const isControlElement = target.closest('button') || 
                           target.closest('[data-resize-handle]') || 
                           target.closest('[role="button"]') ||
                           target.hasAttribute('data-resize-handle');
    
    // Don't allow dragging if clicking on actual control elements
    if (isControlElement) {
      e.preventDefault();
      e.stopPropagation();
      return;
    }
    
    handleDragStartMouse(e);
  }, [handleDragStartMouse]);

  const handleDragStartTouchWrapper = useCallback((e: React.TouchEvent) => {
    // Check if the target is a control element (button, resize handle, etc.)
    const target = e.target as HTMLElement;
    const isControlElement = target.closest('button') || 
                           target.closest('[data-resize-handle]') || 
                           target.closest('[role="button"]') ||
                           target.hasAttribute('data-resize-handle');
    
    // Don't allow dragging if touching actual control elements
    if (isControlElement) {
      e.preventDefault();
      e.stopPropagation();
      return;
    }
    
    handleDragStartTouch(e);
  }, [handleDragStartTouch]);

  const handleDoubleClickWrapper = useCallback((e: React.MouseEvent) => {
    // Check if the target is a control element
    const target = e.target as HTMLElement;
    const isControlElement = target.closest('button') || 
                           target.closest('[data-resize-handle]') || 
                           target.closest('[role="button"]') ||
                           target.hasAttribute('data-resize-handle');
    
    // Don't allow double click actions if clicking on control elements
    if (isControlElement) {
      e.preventDefault();
      e.stopPropagation();
      return;
    }
    
    handleDoubleClick(e);
  }, [handleDoubleClick]);

  // After the handleKeyDown callback, add the handleSwitchPhase function back
  const handleSwitchPhase = useCallback((direction: 'prev' | 'next') => {
    const phases: TimerPhase[] = ['pomodoro', 'shortBreak', 'longBreak'];
    const currentIndex = phases.indexOf(currentPhase as TimerPhase);

    let newIndex: number;
    if (direction === 'prev') {
      // Loop back to the end if at the beginning
      newIndex = currentIndex <= 0 ? phases.length - 1 : currentIndex - 1;
    } else {
      // Loop back to the beginning if at the end
      newIndex = currentIndex >= phases.length - 1 ? 0 : currentIndex + 1;
    }

    // Get the new phase
    const newPhase = phases[newIndex];
    let newTimeRemaining: number;

    // Set the appropriate time based on the new phase
    switch (newPhase) {
      case 'pomodoro':
        newTimeRemaining = timerSettings.pomodoroMinutes * 60;
        break;
      case 'shortBreak':
        newTimeRemaining = timerSettings.shortBreakMinutes * 60;
        break;
      case 'longBreak':
        newTimeRemaining = timerSettings.longBreakMinutes * 60;
        break;
      default:
        newTimeRemaining = timerSettings.pomodoroMinutes * 60;
    }

    // Determine if we need to update pomodoroCount
    // We only increment when going from break to pomodoro
    const currentPomodoroCount = usePomodoroStore.getState().pomodoroCount;
    const newPomodoroCount =
      (currentPhase !== 'pomodoro' && newPhase === 'pomodoro')
        ? (currentPhase === 'longBreak' ? 1 : currentPomodoroCount + 1)
        : currentPomodoroCount;

    // Update state through store
    usePomodoroStore.setState({
      currentPhase: newPhase,
      timeRemaining: newTimeRemaining,
      isRunning: false,
      pomodoroCount: newPomodoroCount
    });
  }, [currentPhase, timerSettings]);



  // Add an effect to apply small preset on component mount
  useEffect(() => {
    applyPreset('small');

    // Set default timer appearance to Minimal preset (white color, 45% opacity)
    const setTimerColor = usePomodoroStore.getState().setTimerColor;
    // const setTimerOpacity = usePomodoroStore.getState().setTimerOpacity;

    setTimerColor('white');
    // setTimerOpacity(45);
  }, [applyPreset]);

  // Sign-in prompt handlers
  const handleSignIn = useCallback(() => {
    setShowSignInPrompt(false);
    router.push('/auth/sign-in');
  }, [router]);

  const handleCloseSignInPrompt = useCallback(() => {
    setShowSignInPrompt(false);
  }, []);



  return (
    <TimerAutoBehaviors
      autoStartPomodoros={autoStartPomodoros}
      currentPhase={currentPhase}
      isRunning={isRunning}
      startTimer={startTimer}
    >
      <TimerInteractionManager
        showControls={showControls}
        setShowControls={setShowControls}
        isDragging={isDragging}
        isResizing={isResizing}
        showHandles={showHandles}
        hideHandles={hideHandles}
        timerRef={timerRef}
      >
        {(interactionHandlers: any) => (
          <TimerContainer
            timerRef={timerRef}
            showControls={showControls}
            isRunning={isRunning}
            isDragging={isDragging}
            isResizing={isResizing}
            isResizePreview={isResizePreview}
            showPositionIndicator={showPositionIndicator}
            showResizeHandles={showResizeHandles}
            isResizeHandleHovered={isResizeHandleHovered}
            customPosition={customPosition}
            timerPosition={timerPosition}
            timerSize={timerSize}
            timerUIStyle={timerUIStyle}
            timerOpacity={timerOpacity}

            interactionHandlers={interactionHandlers}
            handleDragStartMouseWrapper={handleDragStartMouseWrapper}
            handleDragStartTouchWrapper={handleDragStartTouchWrapper}
            handleDoubleClickWrapper={handleDoubleClickWrapper}
            handleKeyDown={handleKeyDown}
            handleResizeHandleMouseEnter={handleResizeHandleMouseEnter}
            handleResizeHandleMouseLeave={handleResizeHandleMouseLeave}
            handleResizeStartMouse={handleResizeStartMouse}
            handleResizeStartTouch={handleResizeStartTouch}
            currentPhaseLabel={currentPhaseLabel}
            timeDisplay={timeDisplay}
          >
            {/* Main Timer Display with enhanced visual elements */}
            <div ref={timerDisplayRef} className="w-full">
              {/* Conditionally render timer UI based on timerUIStyle */}
              {timerUIStyle === 'circular' ? (
                <CircularTimer
                  formattedTime={timeDisplay}
                  phaseLabel={currentPhaseLabel}
                  isRunning={isRunning}
                  currentPhase={currentPhase}
                  pomodoroCount={pomodoroCount}
                  timerColor={timerColor}
                  showControls={showControls}
                  progressPercentage={currentProgress}
                  timerSize={timerSize}
                  fontSize={fontSize}
                  timerSettings={timerSettings}
                  onSwitchPhase={handleSwitchPhase}
                  currentTask={currentTask}
                />
              ) : (
                <TimerMainDisplay
                  formattedTime={timeDisplay}
                  phaseLabel={currentPhaseLabel}
                  isRunning={isRunning}
                  currentPhase={currentPhase}
                  pomodoroCount={pomodoroCount}
                  timerColor={timerColor}
                  showControls={showControls}
                  timerSize={timerSize}
                  fontSize={fontSize}
                  timerSettings={timerSettings}
                  onSwitchPhase={handleSwitchPhase}
                  currentTask={currentTask}
                />
              )}
            </div>

            {/* Timer Controls */}
            <TimerControls
              showControls={showControls}
              isDragging={isDragging}
              isRunning={isRunning}
              timerSize={timerSize}
              timerColor={timerColor}
              onTimerToggle={handleTimerToggle}
              onReset={resetTimer}
            />

            {/* Only show progress bar for default UI style and when enabled */}
            {timerUIStyle === 'default' && showProgressBar && (
              <TimerProgressBar
                progressPercentage={currentProgress}
                isRunning={isRunning}
                showControls={showControls}
                timerColor={timerColor}
                timerSize={timerSize}
              />
            )}
          </TimerContainer>
        )}
      </TimerInteractionManager>

      {/* Sign-in prompt for unauthenticated users */}
      <SignInPrompt
        isVisible={showSignInPrompt}
        onClose={handleCloseSignInPrompt}
        onSignIn={handleSignIn}
      />
    </TimerAutoBehaviors>
  );
});