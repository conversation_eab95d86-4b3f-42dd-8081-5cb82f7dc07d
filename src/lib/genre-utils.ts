/**
 * Utility functions for formatting and displaying genre information
 */

/**
 * Format a genre name from ENUM format to display format
 * @param genre - The genre string in ENUM format (e.g., "LOFI", "ACOUSTIC")
 * @returns Formatted genre name (e.g., "LoFi", "Acoustic")
 */
export function formatGenreName(genre: string): string {
  if (!genre) return '';
  
  // Special cases for specific genres
  const specialCases: Record<string, string> = {
    'LOFI': 'LoFi',
    'HIFI': 'HiFi',
    'ELECTRONIC': 'Electronic',
    'ACOUSTIC': 'Acoustic',
    'MEDITATION': 'Meditation',
    'MYSTERIOUS': 'Mysterious',
    'MOTIVATIONAL': 'Motivational',
  };

  if (specialCases[genre.toUpperCase()]) {
    return specialCases[genre.toUpperCase()];
  }

  // Default formatting: capitalize first letter, lowercase the rest
  return genre.charAt(0).toUpperCase() + genre.slice(1).toLowerCase();
}

/**
 * Get a color class for a genre badge
 * @param genre - The genre string
 * @returns Tailwind CSS classes for the genre badge
 */
export function getGenreColorClass(genre: string): string {
  const genreColors: Record<string, string> = {
    'PIANO': 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 border-blue-200 dark:border-blue-700',
    'GUITAR': 'bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300 border-amber-200 dark:border-amber-700',
    'RELAX': 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300 border-green-200 dark:border-green-700',
    'HAPPY': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300 border-yellow-200 dark:border-yellow-700',
    'MEDITATION': 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300 border-purple-200 dark:border-purple-700',
    'LOFI': 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-300 border-indigo-200 dark:border-indigo-700',
    'SAD': 'bg-slate-100 text-slate-800 dark:bg-slate-900/30 dark:text-slate-300 border-slate-200 dark:border-slate-700',
    'JOYFUL': 'bg-pink-100 text-pink-800 dark:bg-pink-900/30 dark:text-pink-300 border-pink-200 dark:border-pink-700',
    'CALM': 'bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-300 border-teal-200 dark:border-teal-700',
    'ENERGETIC': 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300 border-orange-200 dark:border-orange-700',
    'ACOUSTIC': 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900/30 dark:text-emerald-300 border-emerald-200 dark:border-emerald-700',
    'COUNTRY': 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300 border-red-200 dark:border-red-700',
    'ELECTRONIC': 'bg-cyan-100 text-cyan-800 dark:bg-cyan-900/30 dark:text-cyan-300 border-cyan-200 dark:border-cyan-700',
    'INDIE': 'bg-violet-100 text-violet-800 dark:bg-violet-900/30 dark:text-violet-300 border-violet-200 dark:border-violet-700',
    'AMBIENT': 'bg-sky-100 text-sky-800 dark:bg-sky-900/30 dark:text-sky-300 border-sky-200 dark:border-sky-700',
    'CHILL': 'bg-lime-100 text-lime-800 dark:bg-lime-900/30 dark:text-lime-300 border-lime-200 dark:border-lime-700',
    'STUDY': 'bg-stone-100 text-stone-800 dark:bg-stone-900/30 dark:text-stone-300 border-stone-200 dark:border-stone-700',
    'MYSTERIOUS': 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300 border-gray-200 dark:border-gray-700',
    'MOTIVATIONAL': 'bg-rose-100 text-rose-800 dark:bg-rose-900/30 dark:text-rose-300 border-rose-200 dark:border-rose-700',
  };

  return genreColors[genre.toUpperCase()] || 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300 border-gray-200 dark:border-gray-700';
}

/**
 * Truncate genres array for display with a "+" indicator
 * @param genres - Array of genre strings
 * @param maxDisplay - Maximum number of genres to display before truncating
 * @returns Object with visible genres and remaining count
 */
export function truncateGenres(genres: string[], maxDisplay: number = 2): {
  visible: string[];
  remaining: number;
} {
  if (genres.length <= maxDisplay) {
    return {
      visible: genres,
      remaining: 0
    };
  }

  return {
    visible: genres.slice(0, maxDisplay),
    remaining: genres.length - maxDisplay
  };
}
