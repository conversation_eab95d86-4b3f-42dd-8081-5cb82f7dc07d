import { useCallback, useEffect, useState } from 'react';
import { BOTTOM_EDGE_PADDING } from './common/constants';
import { CustomPosition, TimerSize } from './common/types';
import { TimerPositions } from '@/lib/pomodoro-store';

export interface TimerDragManagerProps {
  timerRef: React.RefObject<HTMLDivElement | null>;
  customPosition: CustomPosition | null;
  setCustomPosition: React.Dispatch<React.SetStateAction<CustomPosition | null>>;
  isResizing: boolean;
  showControls: boolean;
  setShowControls: React.Dispatch<React.SetStateAction<boolean>>;
  hideHandles: () => void;
  timerSize: TimerSize | null;
  setTimerPosition: (position: TimerPositions) => void;
}

export function useTimerDragManager({
  timerRef,
  customPosition,
  setCustomPosition,
  showControls,
  setShowControls,
  hideHandles,
  timerSize,
  setTimerPosition
}: TimerDragManagerProps) {
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState<{ x: number; y: number } | null>(null);
  const [isAtBottomEdge, setIsAtBottomEdge] = useState(false);
  const [showPositionIndicator, setShowPositionIndicator] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Detect mobile device
  useEffect(() => {
    const checkMobile = () => {
      const isMobileDevice = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || 
                           window.innerWidth <= 768;
      setIsMobile(isMobileDevice);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Function to recalculate and adjust timer position on viewport changes
  const adjustTimerPosition = useCallback(() => {
    if (!customPosition || !timerRef.current) return;
    
    const windowWidth = window.innerWidth;
    const windowHeight = window.innerHeight;
    const timerWidth = timerRef.current.offsetWidth;
    const timerHeight = timerRef.current.offsetHeight;
    
    // Calculate maximum allowed positions with bottom padding
    const maxTop = windowHeight - timerHeight - BOTTOM_EDGE_PADDING;
    const maxLeft = windowWidth - timerWidth;
    
    // Make sure timer is within viewport bounds
    const safeLeft = Math.max(0, Math.min(customPosition.left, maxLeft));
    const safeTop = Math.max(0, Math.min(customPosition.top, maxTop));
    
    // Only update if position actually changed
    if (safeLeft !== customPosition.left || safeTop !== customPosition.top) {
      setCustomPosition({ top: safeTop, left: safeLeft });
    }
  }, [customPosition, setCustomPosition, timerRef]);

  // Function to calculate the correct bottom position with padding
  const calculateBottomPosition = useCallback(() => {
    if (!timerRef.current) return null;
    
    const viewportHeight = window.innerHeight;
    const timerHeight = timerRef.current.offsetHeight;
    return viewportHeight - timerHeight - BOTTOM_EDGE_PADDING;
  }, [timerRef]);

  // Modified function to handle drag end that preserves bottom edge positioning
  const handleDragEnd = useCallback(() => {
    setIsDragging(false);
    setDragStart(null);
    document.body.style.cursor = 'default';
    // Remove the timer-dragging class to restore normal scroll behavior
    document.body.classList.remove('timer-dragging');
    
    // Remove timer-interacting class if it exists
    if (timerRef.current) {
      timerRef.current.classList.remove('timer-interacting');
    }
    
    // Ensure the timer doesn't move when you stop dragging
    if (customPosition && timerRef.current) {
      // Get final position values
      const finalTop = customPosition.top;
      const finalLeft = customPosition.left;
      
      // Get viewport dimensions
      const viewportHeight = window.innerHeight;
      const viewportWidth = window.innerWidth;
      
      // Get timer dimensions
      const timerHeight = timerRef.current.offsetHeight;
      const timerWidth = timerRef.current.offsetWidth;
      
      // Calculate maximum allowed positions with bottom padding
      const maxTop = viewportHeight - timerHeight - BOTTOM_EDGE_PADDING;
      const maxLeft = viewportWidth - timerWidth;
      
      // On mobile, skip bottom edge snapping logic and allow free positioning
      if (isMobile) {
        // Simple bounds checking without snapping to predefined positions
        const boundedTop = Math.max(0, Math.min(finalTop, maxTop));
        const boundedLeft = Math.max(0, Math.min(finalLeft, maxLeft));
        
        // Apply the final position
        setCustomPosition({ top: boundedTop, left: boundedLeft });
        setIsAtBottomEdge(false);
      } else {
        // Desktop: Keep the original bottom edge snapping behavior
      // Check if timer is positioned near the bottom edge (with a larger detection area of 20px)
      // This increases the "magnetic" area for the bottom edge
      const isNearBottom = (maxTop - finalTop) <= 20;
      
      if (isNearBottom) {
        // Calculate the horizontal position as a percentage of viewport width
        const timerCenter = finalLeft + (timerWidth / 2);
        const positionPercent = (timerCenter / viewportWidth) * 100;
        
        // Map the position percentage to one of our 5 predefined positions
        let newPosition: TimerPositions;
        
        if (positionPercent < 12.5) {
          newPosition = 'bottom-left';
        } else if (positionPercent < 37.5) {
          newPosition = 'bottom-left-center';
        } else if (positionPercent < 62.5) {
          newPosition = 'bottom-center';
        } else if (positionPercent < 87.5) {
          newPosition = 'bottom-right-center';
        } else {
          newPosition = 'bottom-right';
        }
        
        // Set the new position
        setTimerPosition(newPosition);
        
        // Clear custom position to use the predefined position classes
        setCustomPosition(null);
        setIsAtBottomEdge(false);
      } else {
        // Normal positioning - ensure timer is not positioned outside the viewport bounds
        const boundedTop = Math.max(0, Math.min(finalTop, maxTop));
        const boundedLeft = Math.max(0, Math.min(finalLeft, maxLeft));
        
        // Apply the final position
        setCustomPosition({ top: boundedTop, left: boundedLeft });
        setIsAtBottomEdge(false);
        }
      }
      
      // Force a short delay to hide controls after drag ends
      // to prevent immediate repositioning effect
      setTimeout(() => {
        if (!timerRef.current?.matches(':hover')) {
          setShowControls(false);
          hideHandles();
        }
      }, 500);
    }
  }, [customPosition, hideHandles, setCustomPosition, setShowControls, timerRef, setTimerPosition, isMobile]);

  // Handle drag start (mouse)
  const handleDragStartMouse = useCallback((e: React.MouseEvent) => {
    // Only start dragging if it's the main card being clicked, not buttons or resize handles
    if (
      (e.target as HTMLElement).closest('button') || 
      (e.target as HTMLElement).closest('[data-resize-handle]')
    ) {
      return;
    }
    
    e.preventDefault();
    if (timerRef.current) {
      // Don't disable bottom edge snapping immediately
      // We'll handle this in the drag move handler
      
      const rect = timerRef.current.getBoundingClientRect();
      setDragStart({ x: e.clientX - rect.left, y: e.clientY - rect.top });
      setIsDragging(true);
      // Update cursor for better feedback
      document.body.style.cursor = 'grabbing';
      // Add body class to prevent scroll on mobile during drag
      document.body.classList.add('timer-dragging');
    }
  }, [timerRef]);

  // Handle drag start (touch)
  const handleDragStartTouch = useCallback((e: React.TouchEvent) => {
    // Only start dragging if it's the main card being touched, not buttons or resize handles
    if (
      (e.target as HTMLElement).closest('button') || 
      (e.target as HTMLElement).closest('[data-resize-handle]')
    ) {
      return;
    }
    
    // Prevent default to avoid page refresh/pull-to-refresh
    e.preventDefault();
    e.stopPropagation();
    
    if (timerRef.current) {
      // Don't disable bottom edge snapping immediately
      // We'll handle this in the drag move handler
      
      const touch = e.touches[0];
      const rect = timerRef.current.getBoundingClientRect();
      setDragStart({ x: touch.clientX - rect.left, y: touch.clientY - rect.top });
      setIsDragging(true);
      
      // Add body class to prevent scroll on mobile during drag
      document.body.classList.add('timer-dragging');
    }
  }, [timerRef]);

  // Handle drag move (mouse)
  const handleDragMoveMouse = useCallback((e: MouseEvent) => {
    if (isDragging && dragStart && timerRef.current) {
      e.preventDefault(); // Prevent default browser behavior
      
      // Use requestAnimationFrame to optimize rendering
      requestAnimationFrame(() => {
        if (!timerRef.current) return;
        
        // Get viewport dimensions
        const viewportHeight = window.innerHeight;
        const viewportWidth = window.innerWidth;
        
        // Get timer dimensions
        const timerHeight = timerRef.current.offsetHeight;
        const timerWidth = timerRef.current.offsetWidth;
        
        // Calculate raw positions
        const newLeft = e.clientX - dragStart.x;
        const newTop = e.clientY - dragStart.y;
        
        // Calculate maximum allowed positions accounting for control visibility
        const maxTop = viewportHeight - timerHeight - BOTTOM_EDGE_PADDING;
        const maxLeft = viewportWidth - timerWidth;
        
        // On mobile, skip bottom edge snapping logic
        if (isMobile) {
          // Simple bounds checking without magnetic snapping
          const boundedTop = Math.max(0, Math.min(newTop, maxTop));
          const boundedLeft = Math.max(0, Math.min(newLeft, maxLeft));
          
          setCustomPosition({ top: boundedTop, left: boundedLeft });
        } else {
          // Desktop: Keep the original bottom edge snapping behavior
        // Check if we're moving significantly away from the bottom edge
        if (isAtBottomEdge) {
          // If dragged more than 20px from bottom edge, disable bottom edge snapping
          if (Math.abs(newTop - maxTop) > 20) {
            setIsAtBottomEdge(false);
          }
        }
        
        // If the timer is being dragged close to the bottom edge (within 10px),
        // implement a magnetic snapping effect
        let boundedTop;
        if (!isAtBottomEdge && Math.abs(newTop - maxTop) <= 10) {
          // Snap to bottom
          boundedTop = maxTop;
          setIsAtBottomEdge(true);
        } else {
          // Normal positioning
          boundedTop = Math.max(0, Math.min(newTop, maxTop));
        }
        
        // Ensure timer stays within horizontal bounds
        const boundedLeft = Math.max(0, Math.min(newLeft, maxLeft));
        
        setCustomPosition({ top: boundedTop, left: boundedLeft });
        }
      });
    }
  }, [isDragging, dragStart, isAtBottomEdge, setCustomPosition, timerRef, isMobile]);

  // Handle drag move (touch)
  const handleDragMoveTouch = useCallback((e: TouchEvent) => {
    if (isDragging && dragStart && timerRef.current) {
      // Prevent default browser behavior including pull-to-refresh
      e.preventDefault();
      e.stopPropagation();
      
      // Use requestAnimationFrame to optimize rendering
      requestAnimationFrame(() => {
        if (!isDragging || !dragStart || !timerRef.current) return;
        
        // Get viewport dimensions
        const viewportHeight = window.innerHeight;
        const viewportWidth = window.innerWidth;
        
        // Get timer dimensions
        const timerHeight = timerRef.current.offsetHeight;
        const timerWidth = timerRef.current.offsetWidth;
        
        const touch = e.touches[0];
        
        // Calculate raw positions
        const newLeft = touch.clientX - dragStart.x;
        const newTop = touch.clientY - dragStart.y;
        
        // Calculate maximum allowed positions
        const maxTop = viewportHeight - timerHeight - BOTTOM_EDGE_PADDING;
        const maxLeft = viewportWidth - timerWidth;
        
        // On mobile, skip bottom edge snapping logic
        if (isMobile) {
          // Simple bounds checking without magnetic snapping
          const boundedTop = Math.max(0, Math.min(newTop, maxTop));
          const boundedLeft = Math.max(0, Math.min(newLeft, maxLeft));
          
          setCustomPosition({ top: boundedTop, left: boundedLeft });
        } else {
          // Desktop: Keep the original bottom edge snapping behavior
        // Check if we're moving significantly away from the bottom edge
        if (isAtBottomEdge) {
          // If dragged more than 20px from bottom edge, disable bottom edge snapping
          if (Math.abs(newTop - maxTop) > 20) {
            setIsAtBottomEdge(false);
          }
        }
        
        // If the timer is being dragged close to the bottom edge (within 10px),
        // implement a magnetic snapping effect
        let boundedTop;
        if (!isAtBottomEdge && Math.abs(newTop - maxTop) <= 10) {
          // Snap to bottom
          boundedTop = maxTop;
          setIsAtBottomEdge(true);
        } else {
          // Normal positioning
          boundedTop = Math.max(0, Math.min(newTop, maxTop));
        }
        
        // Ensure timer stays within horizontal bounds
        const boundedLeft = Math.max(0, Math.min(newLeft, maxLeft));
        
        setCustomPosition({ top: boundedTop, left: boundedLeft });
        }
      });
    }
  }, [isDragging, dragStart, isAtBottomEdge, setCustomPosition, timerRef, isMobile]);

  // Handle showControls changes specifically for bottom edge positioning
  useEffect(() => {
    // Skip this effect entirely on mobile since we don't want bottom edge snapping
    if (isMobile) return;
    
    // Only run this effect if timer is at bottom edge and not being dragged
    if (!isAtBottomEdge || isDragging || !customPosition || !timerRef.current) {
      return;
    }
    
    // Use a small timeout to allow any CSS transitions to complete
    // This is key to handling the scaling effect when hovering
    const repositionTimeout = setTimeout(() => {
      const bottomPosition = calculateBottomPosition();
      if (bottomPosition === null) return;
      
      // Only update if position has changed significantly
      if (Math.abs(bottomPosition - customPosition.top) > 2) {
        setCustomPosition(prev => {
          if (!prev) return prev;
          return { ...prev, top: bottomPosition };
        });
      }
    }, showControls ? 50 : 300); // Longer delay when hiding controls to match transition
    
    return () => clearTimeout(repositionTimeout);
  }, [showControls, isAtBottomEdge, isDragging, customPosition, calculateBottomPosition, setCustomPosition, timerRef, isMobile]);
  
  // Effect to maintain bottom alignment when timer size changes
  useEffect(() => {
    // Skip this effect entirely on mobile since we don't want bottom edge snapping
    if (isMobile) return;
    
    // Skip repositioning if user is actively dragging
    if (!isAtBottomEdge || !customPosition || !timerRef.current || isDragging) {
      return;
    }

    // Use a debounced update to prevent rapid state changes
    const updateTimer = () => {
      const bottomPosition = calculateBottomPosition();
      if (bottomPosition === null) return;
      
      // Skip update if the position change is very small
      if (Math.abs(bottomPosition - customPosition.top) < 2) {
        return;
      }
      
      // Reposition to stay at bottom edge with padding
      setCustomPosition(prev => {
        if (!prev) return prev;
        return { ...prev, top: bottomPosition };
      });
    };
    
    // Use requestAnimationFrame to batch UI updates
    const rafId = requestAnimationFrame(updateTimer);
    
    // Clean up
    return () => cancelAnimationFrame(rafId);
  }, [isAtBottomEdge, timerSize, customPosition, isDragging, calculateBottomPosition, setCustomPosition, timerRef, isMobile]);

  // Effect to check bottom alignment on window resize
  useEffect(() => {
    // Skip this effect entirely on mobile since we don't want bottom edge snapping
    if (isMobile) return;
    
    // Skip entirely if not applicable 
    if (!isAtBottomEdge || !customPosition) {
      return;
    }
    
    let resizeTimeout: number | null = null;
    let lastUpdateTime = 0;
    
    const handleWindowResize = () => {
      // Skip repositioning if user is actively dragging
      if (isDragging) return;
      
      // Throttle updates to a reasonable frequency (60fps at most)
      const now = Date.now();
      if (now - lastUpdateTime < 16) { // ~16ms is roughly 60fps
        if (resizeTimeout) {
          window.clearTimeout(resizeTimeout);
        }
        
        // Debounce the resize event
        resizeTimeout = window.setTimeout(() => {
          lastUpdateTime = Date.now();
          updatePosition();
          resizeTimeout = null;
        }, 50);
        
        return;
      }
      
      lastUpdateTime = now;
      updatePosition();
    };
    
    const updatePosition = () => {
      if (!isAtBottomEdge || !customPosition || !timerRef.current) return;
      
      const bottomPosition = calculateBottomPosition();
      if (bottomPosition === null) return;
      
      // Skip if change is very small (prevents unnecessary renders)
      if (Math.abs(bottomPosition - customPosition.top) < 2) {
        return;
      }
      
      // Reposition to stay at bottom edge with padding
      setCustomPosition(prev => {
        if (!prev) return prev;
        return { ...prev, top: bottomPosition };
      });
    };
    
    // Add a throttled, passive event listener
    window.addEventListener('resize', handleWindowResize, { passive: true });
    
    return () => {
      window.removeEventListener('resize', handleWindowResize);
      if (resizeTimeout) {
        window.clearTimeout(resizeTimeout);
      }
    };
  }, [isAtBottomEdge, customPosition, isDragging, calculateBottomPosition, setCustomPosition, timerRef, isMobile]);

  // Add global event listeners for drag movement and end
  useEffect(() => {
    if (isDragging) {
      window.addEventListener('mousemove', handleDragMoveMouse, { passive: false });
      window.addEventListener('mouseup', handleDragEnd);
      // Use passive: false for touch events to allow preventDefault during drag
      window.addEventListener('touchmove', handleDragMoveTouch, { passive: false });
      window.addEventListener('touchend', handleDragEnd, { passive: false });
    }
    
    return () => {
      window.removeEventListener('mousemove', handleDragMoveMouse);
      window.removeEventListener('mouseup', handleDragEnd);
      window.removeEventListener('touchmove', handleDragMoveTouch);
      window.removeEventListener('touchend', handleDragEnd);
    };
  }, [isDragging, handleDragMoveMouse, handleDragMoveTouch, handleDragEnd]);

  // Double-click handler for instant center positioning
  const handleDoubleClick = useCallback((e: React.MouseEvent) => {
    // Only handle if not clicking on controls
    if (
      (e.target as HTMLElement).closest('button') || 
      (e.target as HTMLElement).closest('[data-resize-handle]')
    ) {
      return;
    }

    e.preventDefault();
    return true; // Signal that a double-click was handled
  }, []);

  return {
    isDragging,
    isAtBottomEdge,
    showPositionIndicator,
    handleDragStartMouse,
    handleDragStartTouch,
    handleDragMoveMouse,
    handleDragMoveTouch,
    handleDragEnd,
    handleDoubleClick,
    adjustTimerPosition,
    calculateBottomPosition,
    setShowPositionIndicator
  };
} 