'use client';

import { useEffect } from 'react';
import { usePomodoroStore } from '@/lib/pomodoro-store';
import { useGlobalSystemPlayer } from '@/app/timer/_components/musics-control/use-global-system-player';

/**
 * Mobile Audio Manager - Handles audio logic independently of UI
 * This component is always mounted on mobile to ensure playlist updates
 * are received even when the music control UI is not visible
 */
export function MobileAudioManager() {
  const selectedVideo = usePomodoroStore((state) => state.selectedVideo);

  // Use the global system player hook to manage audio
  const {
    // We don't need to expose the UI controls, just maintain the audio state
    playerId,
    hasGlobalControl,
    isGloballyActive,
  } = useGlobalSystemPlayer(selectedVideo?.playlist || null);

  // Debug logging for mobile audio manager
  useEffect(() => {
    console.log('MobileAudioManager: Playlist updated:', selectedVideo?.playlist?.name, `(ID: ${selectedVideo?.playlist?.id})`);
    console.log('MobileAudioManager: Has global control:', hasGlobalControl, 'Is globally active:', isGloballyActive);
  }, [selectedVideo?.playlist?.id, selectedVideo?.playlist?.name, hasGlobalControl, isGloballyActive]);

  // This component doesn't render any UI, it just manages audio state
  return null;
}
